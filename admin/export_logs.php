<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

$type = $_GET['type'] ?? 'system';
$date_range = $_GET['date_range'] ?? 'month';
$log_level = $_GET['log_level'] ?? '';
$user_filter = $_GET['user_filter'] ?? '';
$ip_filter = $_GET['ip_filter'] ?? '';
$start_date = $_GET['start_date'] ?? '';
$end_date = $_GET['end_date'] ?? '';
$export_format = $_GET['export'] ?? 'csv';

// Build date condition
$date_condition = '';
switch ($date_range) {
    case 'today':
        $date_condition = "AND DATE(created_at) = CURDATE()";
        break;
    case 'week':
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        break;
    case 'month':
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        break;
    case 'all':
        $date_condition = ""; // No date restriction
        break;
    case 'custom':
        if (!empty($start_date) && !empty($end_date)) {
            $date_condition = "AND DATE(created_at) BETWEEN " . $pdo->quote($start_date) . " AND " . $pdo->quote($end_date);
        } elseif (!empty($start_date)) {
            $date_condition = "AND DATE(created_at) >= " . $pdo->quote($start_date);
        } elseif (!empty($end_date)) {
            $date_condition = "AND DATE(created_at) <= " . $pdo->quote($end_date);
        }
        break;
    default:
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        break;
}

// Build additional conditions
$additional_conditions = '';

// Log level filter (works for different log types)
if (!empty($log_level)) {
    switch ($type) {
        case 'system':
            $additional_conditions .= " AND log_level = " . $pdo->quote($log_level);
            break;
        case 'security':
        case 'access':
        case 'file_operations':
        case 'suspicious':
            $additional_conditions .= " AND risk_level = " . $pdo->quote($log_level);
            break;
        case 'anomaly':
            $additional_conditions .= " AND severity = " . $pdo->quote($log_level);
            break;
    }
}

// User filter
if (!empty($user_filter)) {
    switch ($type) {
        case 'admin_actions':
            $additional_conditions .= " AND (admin_username LIKE " . $pdo->quote('%' . $user_filter . '%') .
                                     " OR target_username LIKE " . $pdo->quote('%' . $user_filter . '%') . ")";
            break;
        case 'login':
            $additional_conditions .= " AND email LIKE " . $pdo->quote('%' . $user_filter . '%');
            break;
        default:
            $additional_conditions .= " AND username LIKE " . $pdo->quote('%' . $user_filter . '%');
            break;
    }
}

// IP address filter
if (!empty($ip_filter)) {
    $additional_conditions .= " AND ip_address LIKE " . $pdo->quote('%' . $ip_filter . '%');
}

// Set headers for CSV download
$filename = $type . '_logs_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// Create output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8 (helps with Excel compatibility)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

try {
    switch ($type) {
        case 'system':
            $query = "
                SELECT log_level, category, message, details, ip_address, user_agent, created_at
                FROM system_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
            ";
            $headers = ['Log Level', 'Category', 'Message', 'Details', 'IP Address', 'User Agent', 'Created At'];
            break;

        case 'security':
            $query = "
                SELECT event_type, username, ip_address, user_agent, details, risk_level, created_at
                FROM security_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
            ";
            $headers = ['Event Type', 'Username', 'IP Address', 'User Agent', 'Details', 'Risk Level', 'Created At'];
            break;

        case 'audit':
            $query = "
                SELECT action_type, table_name, record_id, username, old_values, new_values, ip_address, created_at
                FROM audit_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
            ";
            $headers = ['Action Type', 'Table Name', 'Record ID', 'Username', 'Old Values', 'New Values', 'IP Address', 'Created At'];
            break;

        case 'application':
            $query = "
                SELECT module, action, user_id, request_method, request_uri, response_code, execution_time, memory_usage, ip_address, created_at
                FROM application_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
            ";
            $headers = ['Module', 'Action', 'User ID', 'Request Method', 'Request URI', 'Response Code', 'Execution Time', 'Memory Usage', 'IP Address', 'Created At'];
            break;

        case 'error':
            $query = "
                SELECT error_type, error_message, file_path, line_number, stack_trace, user_id, session_id, ip_address, created_at
                FROM error_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
            ";
            $headers = ['Error Type', 'Error Message', 'File Path', 'Line Number', 'Stack Trace', 'User ID', 'Session ID', 'IP Address', 'Created At'];
            break;

        default:
            fputcsv($output, ['Error: Invalid log type']);
            fclose($output);
            exit;
    }

    // Write headers
    fputcsv($output, $headers);

    // Execute query and write data
    $stmt = $pdo->prepare($query);
    $stmt->execute();

    $row_count = 0;
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // Process each field
        $csv_row = [];
        foreach ($row as $key => $value) {
            if (is_null($value)) {
                $csv_row[] = '';
            } elseif (in_array($key, ['details', 'old_values', 'new_values', 'stack_trace']) && !empty($value)) {
                // Format JSON fields for CSV
                $json_data = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $csv_row[] = json_encode($json_data, JSON_PRETTY_PRINT);
                } else {
                    $csv_row[] = $value;
                }
            } else {
                $csv_row[] = $value;
            }
        }
        
        fputcsv($output, $csv_row);
        $row_count++;
    }

    // Add summary information
    fputcsv($output, []); // Empty row
    fputcsv($output, ['EXPORT SUMMARY']);
    fputcsv($output, ['Log Type', ucfirst($type) . ' Logs']);
    fputcsv($output, ['Date Range', ucfirst($date_range)]);
    if ($date_range === 'custom') {
        if (!empty($start_date)) fputcsv($output, ['Start Date', $start_date]);
        if (!empty($end_date)) fputcsv($output, ['End Date', $end_date]);
    }
    fputcsv($output, ['Total Records', $row_count]);
    fputcsv($output, ['Export Date', date('Y-m-d H:i:s')]);
    fputcsv($output, ['Exported By', $_SESSION['username'] ?? 'Admin']);

    // Add filter information
    if (!empty($log_level)) {
        fputcsv($output, ['Log Level Filter', $log_level]);
    }
    if (!empty($user_filter)) {
        fputcsv($output, ['User Filter', $user_filter]);
    }
    if (!empty($ip_filter)) {
        fputcsv($output, ['IP Filter', $ip_filter]);
    }

    fclose($output);

} catch (Exception $e) {
    // Clear any output and send error
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    header('Content-Type: text/plain');
    echo 'Export Error: ' . $e->getMessage();
}
?>
