<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Validate input
if (!isset($_POST['action']) || $_POST['action'] !== 'fix_missing_badges') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit;
}

try {
    $pdo->beginTransaction();
    
    // Find users who completed categories but don't have badges
    $stmt = $pdo->query("
        SELECT 
            u.id as user_id,
            u.username,
            cat.id as category_id,
            cat.name as category_name,
            COUNT(DISTINCT c.id) as total_challenges,
            COUNT(DISTINCT up.challenge_id) as completed_challenges
        FROM users u
        JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
        JOIN challenges c ON up.challenge_id = c.id
        JOIN categories cat ON c.category_id = cat.id
        LEFT JOIN user_badges ub ON u.id = ub.user_id AND cat.id = ub.category_id
        LEFT JOIN badges b ON ub.badge_id = b.id AND b.requirement_type = 'category_complete'
        WHERE ub.id IS NULL
        GROUP BY u.id, cat.id, u.username, cat.name
        HAVING completed_challenges = total_challenges AND total_challenges > 0
        ORDER BY u.username, cat.name
    ");
    $missing_badges = $stmt->fetchAll();
    
    $badges_awarded = 0;
    $users_affected = [];
    
    foreach ($missing_badges as $missing) {
        $user_id = $missing['user_id'];
        $category_id = $missing['category_id'];
        $username = $missing['username'];
        $category_name = $missing['category_name'];
        
        // Check if badge exists for this category
        $stmt = $pdo->prepare("
            SELECT id FROM badges 
            WHERE category_id = ? AND requirement_type = 'category_complete'
        ");
        $stmt->execute([$category_id]);
        $badge = $stmt->fetch();
        
        if (!$badge) {
            // Create a default badge for this category
            $badge_name = $category_name . ' Master';
            $badge_description = 'Completed all ' . $category_name . ' challenges';
            
            // Get category info for badge styling
            $stmt = $pdo->prepare("SELECT icon, color FROM categories WHERE id = ?");
            $stmt->execute([$category_id]);
            $cat_info = $stmt->fetch();
            
            $badge_icon = $cat_info['icon'] ?: '🏆';
            $badge_color = $cat_info['color'] ?: '#007bff';
            
            $stmt = $pdo->prepare("
                INSERT INTO badges (name, description, icon, color, category_id, requirement_type)
                VALUES (?, ?, ?, ?, ?, 'category_complete')
            ");
            $stmt->execute([$badge_name, $badge_description, $badge_icon, $badge_color, $category_id]);
            $badge_id = $pdo->lastInsertId();
        } else {
            $badge_id = $badge['id'];
        }
        
        // Award the badge
        $stmt = $pdo->prepare("
            INSERT INTO user_badges (user_id, badge_id, category_id, earned_at)
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$user_id, $badge_id, $category_id]);
        
        $badges_awarded++;
        if (!in_array($username, $users_affected)) {
            $users_affected[] = $username;
        }
        
        // Log the badge award
        $stmt = $pdo->prepare("
            INSERT INTO admin_actions_logs 
            (admin_username, action_type, action_description, target_user_id, target_username, ip_address, user_agent, success, created_at)
            VALUES (?, 'BADGE_AUTO_FIXED', ?, ?, ?, ?, ?, 1, NOW())
        ");
        $stmt->execute([
            $_SESSION['username'],
            "Auto-awarded missing badge for category: $category_name (completed $missing[completed_challenges]/$missing[total_challenges] challenges)",
            $user_id,
            $username,
            $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ]);
    }
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'badges_awarded' => $badges_awarded,
        'users_affected' => count($users_affected),
        'details' => [
            'missing_badges_found' => count($missing_badges),
            'users_list' => array_slice($users_affected, 0, 10) // Limit for response size
        ]
    ]);
    
} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Error fixing missing badges: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred while fixing badges'
    ]);
} catch (Exception $e) {
    $pdo->rollBack();
    error_log("Error fixing missing badges: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred: ' . $e->getMessage()
    ]);
}
?>
