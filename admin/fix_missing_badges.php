<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Validate input
if (!isset($_POST['action']) || $_POST['action'] !== 'fix_missing_badges') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit;
}

try {
    // First, let's check if all required tables exist
    $required_tables = ['users', 'categories', 'challenges', 'user_progress', 'badges', 'user_badges'];
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            throw new Exception("Required table '$table' does not exist");
        }
    }

    $pdo->beginTransaction();

    // Find users who completed categories but don't have badges
    // Step 1: Get all category completions
    $stmt = $pdo->query("
        SELECT
            u.id as user_id,
            u.username,
            cat.id as category_id,
            cat.name as category_name,
            (SELECT COUNT(*) FROM challenges WHERE category_id = cat.id) as total_challenges,
            (SELECT COUNT(*) FROM user_progress up
             JOIN challenges c ON up.challenge_id = c.id
             WHERE up.user_id = u.id AND c.category_id = cat.id AND up.status = 'completed'
            ) as completed_challenges
        FROM users u
        CROSS JOIN categories cat
        WHERE u.role = 'student'
        HAVING total_challenges > 0
        AND completed_challenges = total_challenges
        AND completed_challenges > 0
    ");
    $all_completions = $stmt->fetchAll();

    // Step 2: Filter out those who already have badges
    $missing_badges = [];
    foreach ($all_completions as $completion) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM user_badges ub
            JOIN badges b ON ub.badge_id = b.id
            WHERE ub.user_id = ? AND b.category_id = ?
        ");
        $stmt->execute([$completion['user_id'], $completion['category_id']]);
        $has_badge = $stmt->fetchColumn() > 0;

        if (!$has_badge) {
            $missing_badges[] = $completion;
        }
    }
    
    $badges_awarded = 0;
    $users_affected = [];
    
    foreach ($missing_badges as $missing) {
        $user_id = $missing['user_id'];
        $category_id = $missing['category_id'];
        $username = $missing['username'];
        $category_name = $missing['category_name'];
        
        // Check if badge exists for this category
        $stmt = $pdo->prepare("SELECT id FROM badges WHERE category_id = ?");
        $stmt->execute([$category_id]);
        $badge = $stmt->fetch();

        if (!$badge) {
            // Create a default badge for this category
            $badge_name = $category_name . ' Master';
            $badge_description = 'Completed all ' . $category_name . ' challenges';

            // Get category info for badge styling
            $stmt = $pdo->prepare("SELECT icon FROM categories WHERE id = ?");
            $stmt->execute([$category_id]);
            $cat_info = $stmt->fetch();

            $badge_icon = $cat_info['icon'] ?: '🏆';

            // Check what columns exist in badges table
            $stmt = $pdo->query("DESCRIBE badges");
            $badge_columns = array_column($stmt->fetchAll(), 'Field');

            if (in_array('requirement_type', $badge_columns) && in_array('color', $badge_columns)) {
                // Full schema
                $stmt = $pdo->prepare("
                    INSERT INTO badges (name, description, icon, color, category_id, requirement_type)
                    VALUES (?, ?, ?, ?, ?, 'category_complete')
                ");
                $stmt->execute([$badge_name, $badge_description, $badge_icon, '#007bff', $category_id]);
            } elseif (in_array('requirement_type', $badge_columns)) {
                // No color column
                $stmt = $pdo->prepare("
                    INSERT INTO badges (name, description, icon, category_id, requirement_type)
                    VALUES (?, ?, ?, ?, 'category_complete')
                ");
                $stmt->execute([$badge_name, $badge_description, $badge_icon, $category_id]);
            } else {
                // Basic schema
                $stmt = $pdo->prepare("
                    INSERT INTO badges (name, description, icon, category_id)
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$badge_name, $badge_description, $badge_icon, $category_id]);
            }

            $badge_id = $pdo->lastInsertId();
        } else {
            $badge_id = $badge['id'];
        }
        
        // Award the badge - check schema first
        $stmt = $pdo->query("DESCRIBE user_badges");
        $user_badge_columns = array_column($stmt->fetchAll(), 'Field');

        if (in_array('category_id', $user_badge_columns)) {
            // Full schema with category_id
            $stmt = $pdo->prepare("
                INSERT INTO user_badges (user_id, badge_id, category_id, earned_at)
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$user_id, $badge_id, $category_id]);
        } else {
            // Basic schema without category_id
            $stmt = $pdo->prepare("
                INSERT INTO user_badges (user_id, badge_id, earned_at)
                VALUES (?, ?, NOW())
            ");
            $stmt->execute([$user_id, $badge_id]);
        }
        
        $badges_awarded++;
        if (!in_array($username, $users_affected)) {
            $users_affected[] = $username;
        }
        
        // Log the badge award (check if admin_actions_logs table exists and has correct structure)
        try {
            // Get admin user ID
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND role = 'admin'");
            $stmt->execute([$_SESSION['username']]);
            $admin_user = $stmt->fetch();
            $admin_id = $admin_user ? $admin_user['id'] : 1; // Fallback to ID 1 if not found

            $stmt = $pdo->prepare("
                INSERT INTO admin_actions_logs
                (admin_id, admin_username, action_type, action_description, target_user_id, target_username, ip_address, user_agent, success, created_at)
                VALUES (?, ?, 'BADGE_MANAGEMENT', ?, ?, ?, ?, ?, 1, NOW())
            ");
            $stmt->execute([
                $admin_id,
                $_SESSION['username'],
                "Auto-awarded missing badge for category: $category_name (completed {$missing['completed_challenges']}/{$missing['total_challenges']} challenges)",
                $user_id,
                $username,
                $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ]);
        } catch (PDOException $log_error) {
            // If logging fails, continue anyway - don't fail the badge award
            error_log("Failed to log badge award: " . $log_error->getMessage());
        }
    }
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'badges_awarded' => $badges_awarded,
        'users_affected' => count($users_affected),
        'details' => [
            'missing_badges_found' => count($missing_badges),
            'users_list' => array_slice($users_affected, 0, 10) // Limit for response size
        ]
    ]);
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    $error_details = [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];

    error_log("PDO Error fixing missing badges: " . json_encode($error_details));

    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred while fixing badges',
        'debug' => $_SESSION['user_role'] === 'admin' ? $error_details : null
    ]);
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    $error_details = [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];

    error_log("General Error fixing missing badges: " . json_encode($error_details));

    echo json_encode([
        'success' => false,
        'message' => 'An error occurred: ' . $e->getMessage(),
        'debug' => $_SESSION['user_role'] === 'admin' ? $error_details : null
    ]);
}
?>
