<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Brute Force Audit Trail Migration</h1>";
echo "<p>This script will update the brute force protection system to preserve audit trails.</p>";

try {
    // Check current table structure
    echo "<h2>1. Checking Current Table Structure</h2>";
    
    $stmt = $pdo->query("DESCRIBE account_lockouts");
    $columns = array_column($stmt->fetchAll(), 'Field');
    echo "Current columns: " . implode(', ', $columns) . "<br><br>";
    
    // Add missing columns
    echo "<h2>2. Adding Missing Columns</h2>";
    
    $newColumns = [
        'manually_unlocked' => [
            'sql' => 'ADD COLUMN manually_unlocked TINYINT(1) DEFAULT 0',
            'description' => 'Flag to indicate if account was manually unlocked by admin'
        ],
        'unlocked_by' => [
            'sql' => 'ADD COLUMN unlocked_by VARCHAR(255) NULL',
            'description' => 'Username of admin who unlocked the account'
        ],
        'unlocked_at' => [
            'sql' => 'ADD COLUMN unlocked_at TIMESTAMP NULL',
            'description' => 'Timestamp when account was manually unlocked'
        ],
        'unlock_reason' => [
            'sql' => 'ADD COLUMN unlock_reason VARCHAR(500) NULL',
            'description' => 'Reason provided for manual unlock'
        ],
        'status' => [
            'sql' => 'ADD COLUMN status ENUM(\'active\', \'expired\', \'manually_unlocked\') DEFAULT \'active\'',
            'description' => 'Current status of the lockout record'
        ]
    ];
    
    foreach ($newColumns as $columnName => $columnInfo) {
        if (!in_array($columnName, $columns)) {
            try {
                $pdo->exec("ALTER TABLE account_lockouts " . $columnInfo['sql']);
                echo "✅ Added column '$columnName': " . $columnInfo['description'] . "<br>";
            } catch (Exception $e) {
                echo "❌ Failed to add column '$columnName': " . $e->getMessage() . "<br>";
            }
        } else {
            echo "ℹ️ Column '$columnName' already exists<br>";
        }
    }
    
    // Add indexes
    echo "<br><h2>3. Adding Indexes</h2>";
    
    try {
        $pdo->exec("ALTER TABLE account_lockouts ADD INDEX idx_status (status)");
        echo "✅ Added index on status column<br>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "ℹ️ Index on status column already exists<br>";
        } else {
            echo "❌ Failed to add status index: " . $e->getMessage() . "<br>";
        }
    }
    
    // Update existing records
    echo "<br><h2>4. Updating Existing Records</h2>";
    
    // Set status for existing records
    $stmt = $pdo->exec("
        UPDATE account_lockouts 
        SET status = CASE 
            WHEN unlock_at > NOW() THEN 'active'
            ELSE 'expired'
        END
        WHERE status IS NULL OR status = ''
    ");
    echo "✅ Updated status for $stmt existing records<br>";
    
    // Test the new functionality
    echo "<br><h2>5. Testing New Functionality</h2>";
    
    // Test manual unlock functionality
    require '../config/brute_force_protection.php';
    $bruteForceProtection = new BruteForceProtection($pdo);
    
    echo "✅ BruteForceProtection class loaded successfully<br>";
    echo "✅ New manualUnlock method available<br>";
    
    // Show current statistics
    $stats = $bruteForceProtection->getLockoutStats();
    echo "✅ Current lockout statistics:<br>";
    echo "&nbsp;&nbsp;&nbsp;- Active lockouts: " . ($stats['current_locked'] ?? 0) . "<br>";
    echo "&nbsp;&nbsp;&nbsp;- Recent attempts: " . ($stats['recent_attempts'] ?? 0) . "<br>";
    
    // Show sample data
    echo "<br><h2>6. Sample Data</h2>";
    
    $stmt = $pdo->query("
        SELECT email, locked_at, unlock_at, status, manually_unlocked, unlocked_by
        FROM account_lockouts 
        ORDER BY locked_at DESC 
        LIMIT 5
    ");
    $samples = $stmt->fetchAll();
    
    if (empty($samples)) {
        echo "ℹ️ No lockout records found<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Email</th><th>Locked At</th><th>Status</th><th>Manually Unlocked</th><th>Unlocked By</th></tr>";
        foreach ($samples as $sample) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($sample['email']) . "</td>";
            echo "<td>" . $sample['locked_at'] . "</td>";
            echo "<td>" . $sample['status'] . "</td>";
            echo "<td>" . ($sample['manually_unlocked'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . htmlspecialchars($sample['unlocked_by'] ?? '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<br><h2>7. Migration Complete</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>✅ Migration completed successfully!</strong><br><br>";
    echo "<strong>What's New:</strong><br>";
    echo "• Account unlocks now preserve audit trails<br>";
    echo "• Manual unlocks are tracked with admin username and reason<br>";
    echo "• Lockout history shows detailed status information<br>";
    echo "• Enhanced UI with unlock modal and status indicators<br>";
    echo "• Extended data retention (30 days instead of immediate deletion)<br><br>";
    echo "<strong>Next Steps:</strong><br>";
    echo "• Visit the <a href='brute_force_management.php'>Brute Force Management</a> page<br>";
    echo "• Test the new unlock functionality<br>";
    echo "• Review the enhanced audit trail features<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>❌ Migration failed:</strong><br>";
    echo htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "<br><a href='brute_force_management.php' class='btn btn-primary'>← Back to Brute Force Management</a>";
?>
