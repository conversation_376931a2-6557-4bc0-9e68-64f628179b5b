<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

$log_id = $_GET['id'] ?? null;
$log_type = $_GET['type'] ?? null;

if (!$log_id || !$log_type) {
    echo json_encode(['success' => false, 'message' => 'Log ID and type required']);
    exit;
}

try {
    $details = [];
    
    switch ($log_type) {
        case 'security':
            $stmt = $pdo->prepare("SELECT * FROM security_logs WHERE id = ?");
            $stmt->execute([$log_id]);
            $log = $stmt->fetch();
            
            if ($log) {
                $details = [
                    'Event Type' => $log['event_type'],
                    'Username' => $log['username'] ?? 'System',
                    'User ID' => $log['user_id'] ?? 'N/A',
                    'IP Address' => $log['ip_address'],
                    'Country' => $log['country'],
                    'City' => $log['city'],
                    'User Agent' => $log['user_agent'],
                    'Session ID' => $log['session_id'],
                    'Risk Level' => $log['risk_level'],
                    'Details' => json_decode($log['details'], true),
                    'Created At' => $log['created_at']
                ];
            }
            break;
            
        case 'anomaly':
            $stmt = $pdo->prepare("SELECT * FROM anomaly_logs WHERE id = ?");
            $stmt->execute([$log_id]);
            $log = $stmt->fetch();
            
            if ($log) {
                $details = [
                    'Anomaly Type' => $log['anomaly_type'],
                    'User ID' => $log['user_id'] ?? 'N/A',
                    'IP Address' => $log['ip_address'],
                    'Severity' => $log['severity'],
                    'Description' => $log['description'],
                    'Data' => json_decode($log['data'], true),
                    'Resolved' => $log['resolved'] ? 'Yes' : 'No',
                    'Created At' => $log['created_at']
                ];
            }
            break;
            
        case 'login':
            $stmt = $pdo->prepare("SELECT * FROM login_attempts WHERE id = ?");
            $stmt->execute([$log_id]);
            $log = $stmt->fetch();

            if ($log) {
                $details = [
                    'Email' => $log['email'],
                    'IP Address' => $log['ip_address'],
                    'Country' => $log['country'],
                    'City' => $log['city'],
                    'User Agent' => $log['user_agent'],
                    'Success' => $log['success'] ? 'Yes' : 'No',
                    'Failure Reason' => $log['failure_reason'] ?? 'N/A',
                    'Session Duration' => $log['session_duration'] ?? 'N/A',
                    'Created At' => $log['created_at']
                ];
            }
            break;

        case 'audit':
            $stmt = $pdo->prepare("SELECT * FROM audit_logs WHERE id = ?");
            $stmt->execute([$log_id]);
            $log = $stmt->fetch();

            if ($log) {
                $old_values = json_decode($log['old_values'], true);
                $new_values = json_decode($log['new_values'], true);

                $details = [
                    'Action Type' => $log['action_type'],
                    'Table Name' => $log['table_name'],
                    'Record ID' => $log['record_id'],
                    'Username' => $log['username'] ?? 'System',
                    'IP Address' => $log['ip_address'],
                    'User Agent' => $log['user_agent'] ?? 'N/A',
                    'Old Values' => $old_values,
                    'New Values' => $new_values,
                    'Changes Summary' => generateChangesSummary($old_values, $new_values),
                    'Created At' => $log['created_at']
                ];
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid log type']);
            exit;
    }
    
    if (empty($details)) {
        echo json_encode(['success' => false, 'message' => 'Log not found']);
    } else {
        echo json_encode(['success' => true, 'details' => $details]);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}

/**
 * Generate a human-readable summary of changes
 */
function generateChangesSummary($old_values, $new_values) {
    if (!is_array($old_values) || !is_array($new_values)) {
        return 'Invalid change data';
    }

    $changes = [];

    // Find all fields that changed
    $all_fields = array_unique(array_merge(array_keys($old_values), array_keys($new_values)));

    foreach ($all_fields as $field) {
        $old_val = $old_values[$field] ?? '[not set]';
        $new_val = $new_values[$field] ?? '[not set]';

        if ($old_val !== $new_val) {
            // Special handling for sensitive fields
            if (in_array($field, ['password_hash', 'password'])) {
                $changes[] = "$field: [REDACTED] → [REDACTED]";
            } else {
                $changes[] = "$field: '$old_val' → '$new_val'";
            }
        }
    }

    return empty($changes) ? 'No changes detected' : implode(', ', $changes);
}
?>
