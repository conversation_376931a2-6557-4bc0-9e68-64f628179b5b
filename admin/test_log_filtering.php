<?php
// Test Log Filtering Functionality
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Test Log Filtering</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='container mt-5'>";

echo "<h1>🔍 Test Log Filtering Functionality</h1>";
echo "<p class='text-muted'>Testing the enhanced filtering system for admin logs</p>";

try {
    echo "<h2>Step 1: Check Available Log Data</h2>";
    
    // Check what log data is available
    $log_types = [
        'system_logs' => 'System Logs',
        'security_logs' => 'Security Logs', 
        'audit_logs' => 'Audit Logs',
        'login_attempts' => 'Login Attempts',
        'anomaly_logs' => 'Anomaly Logs'
    ];
    
    echo "<div class='row'>";
    foreach ($log_types as $table => $name) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetchColumn();
        
        echo "<div class='col-md-2'>";
        echo "<div class='card text-center'>";
        echo "<div class='card-body'>";
        echo "<h5 class='card-title'>$count</h5>";
        echo "<p class='card-text'>$name</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>Step 2: Test Filter Parameters</h2>";
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h5>📅 Date Range Filters</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li><strong>Today:</strong> Records from today only</li>";
    echo "<li><strong>Last 7 days:</strong> Records from past week</li>";
    echo "<li><strong>Last 30 days:</strong> Records from past month (default)</li>";
    echo "<li><strong>All Time:</strong> All available records</li>";
    echo "<li><strong>Custom Range:</strong> User-defined date range</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h5>🎯 Filter Types</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li><strong>Log Level:</strong> INFO, WARNING, ERROR, CRITICAL, HIGH, MEDIUM, LOW</li>";
    echo "<li><strong>User Filter:</strong> Filter by username (partial match)</li>";
    echo "<li><strong>IP Filter:</strong> Filter by IP address (partial match)</li>";
    echo "<li><strong>Auto-filtering:</strong> Filters apply automatically as you type</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<h2>Step 3: Test Filter URLs</h2>";
    
    $test_filters = [
        'Basic System Logs' => 'get_logs.php?type=system&limit=10',
        'Security Logs (Last 7 days)' => 'get_logs.php?type=security&date_range=week&limit=10',
        'Audit Logs (All time)' => 'get_logs.php?type=audit&date_range=all&limit=10',
        'High Risk Security Events' => 'get_logs.php?type=security&log_level=HIGH&limit=10',
        'Login Attempts Today' => 'get_logs.php?type=login&date_range=today&limit=10',
        'User Filter Test' => 'get_logs.php?type=audit&user_filter=admin&limit=10'
    ];
    
    echo "<div class='row'>";
    foreach ($test_filters as $name => $url) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>$name</h6>";
        echo "<a href='$url' target='_blank' class='btn btn-sm btn-primary'>Test Filter</a>";
        echo "<p class='card-text mt-2'><small class='text-muted'>$url</small></p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>Step 4: Test Export Functionality</h2>";
    
    $export_tests = [
        'Export System Logs (CSV)' => 'export_logs.php?type=system&export=csv&limit=50',
        'Export Security Logs (Filtered)' => 'export_logs.php?type=security&date_range=week&log_level=HIGH&export=csv',
        'Export Audit Logs (All Time)' => 'export_logs.php?type=audit&date_range=all&export=csv&limit=100'
    ];
    
    echo "<div class='row'>";
    foreach ($export_tests as $name => $url) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>$name</h6>";
        echo "<a href='$url' target='_blank' class='btn btn-sm btn-success'>Download CSV</a>";
        echo "<p class='card-text mt-2'><small class='text-muted'>$url</small></p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>Step 5: Interactive Filter Testing</h2>";
    
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h5>🧪 Live Filter Test</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<div class='row g-3'>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Log Type</label>";
    echo "<select class='form-select' id='testLogType'>";
    echo "<option value='system'>System Logs</option>";
    echo "<option value='security'>Security Logs</option>";
    echo "<option value='audit'>Audit Logs</option>";
    echo "<option value='login'>Login Attempts</option>";
    echo "</select>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Date Range</label>";
    echo "<select class='form-select' id='testDateRange'>";
    echo "<option value='today'>Today</option>";
    echo "<option value='week'>Last 7 days</option>";
    echo "<option value='month' selected>Last 30 days</option>";
    echo "<option value='all'>All Time</option>";
    echo "</select>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>User Filter</label>";
    echo "<input type='text' class='form-control' id='testUserFilter' placeholder='Username'>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Actions</label><br>";
    echo "<button class='btn btn-primary' onclick='testFilter()'>Test Filter</button>";
    echo "</div>";
    echo "</div>";
    echo "<div id='testResults' class='mt-4'></div>";
    echo "</div>";
    echo "</div>";
    
    echo "<h2>Step 6: Real Admin Logs Interface</h2>";
    
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-info-circle me-2'></i>Test the Real Interface</h5>";
    echo "<p>Now test the actual admin logs interface with all the enhanced filtering:</p>";
    echo "<ol>";
    echo "<li>Go to the <a href='logs.php' target='_blank' class='btn btn-sm btn-primary'>Admin Logs</a> page</li>";
    echo "<li>Try different date ranges (Today, Week, Month, All Time, Custom)</li>";
    echo "<li>Test log level filtering (INFO, WARNING, ERROR, etc.)</li>";
    echo "<li>Try user and IP address filtering</li>";
    echo "<li>Test the export functionality</li>";
    echo "<li>Verify auto-filtering works as you type</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Test Failed</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<script>";
echo "function testFilter() {";
echo "    const logType = document.getElementById('testLogType').value;";
echo "    const dateRange = document.getElementById('testDateRange').value;";
echo "    const userFilter = document.getElementById('testUserFilter').value;";
echo "    ";
echo "    const params = new URLSearchParams({";
echo "        type: logType,";
echo "        date_range: dateRange,";
echo "        user_filter: userFilter,";
echo "        limit: 10";
echo "    });";
echo "    ";
echo "    const resultsDiv = document.getElementById('testResults');";
echo "    resultsDiv.innerHTML = '<div class=\"alert alert-info\">🔄 Testing filter...</div>';";
echo "    ";
echo "    fetch(`get_logs.php?\${params}`)";
echo "        .then(response => response.text())";
echo "        .then(data => {";
echo "            resultsDiv.innerHTML = `";
echo "                <h6>Filter Results:</h6>";
echo "                <div class=\"border rounded p-3\" style=\"max-height: 400px; overflow-y: auto;\">";
echo "                    \${data}";
echo "                </div>";
echo "            `;";
echo "        })";
echo "        .catch(error => {";
echo "            resultsDiv.innerHTML = '<div class=\"alert alert-danger\">Error: ' + error.message + '</div>';";
echo "        });";
echo "}";
echo "</script>";

echo "<div class='mt-4 p-3 bg-light rounded'>";
echo "<h3>🎯 Expected Filtering Features</h3>";
echo "<ul>";
echo "<li>✅ <strong>Date Range Filtering:</strong> Today, Week, Month, All Time, Custom Range</li>";
echo "<li>✅ <strong>Log Level Filtering:</strong> Different levels for different log types</li>";
echo "<li>✅ <strong>User Filtering:</strong> Search by username with partial matching</li>";
echo "<li>✅ <strong>IP Address Filtering:</strong> Search by IP address</li>";
echo "<li>✅ <strong>Auto-filtering:</strong> Filters apply automatically as you type</li>";
echo "<li>✅ <strong>Filter Status:</strong> Shows active filters with clear option</li>";
echo "<li>✅ <strong>Export Functionality:</strong> CSV export with applied filters</li>";
echo "<li>✅ <strong>Custom Date Range:</strong> Date picker for specific periods</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
