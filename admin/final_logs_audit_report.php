<?php
// Final Comprehensive Admin Logs Audit Report
session_start();
require '../config/db_connect.php';
require '../config/logger.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../auth/signin.php');
    exit;
}

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Final Admin Logs Audit Report</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo ".audit-card { background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin: 20px 0; padding: 25px; }";
echo ".status-good { color: #28a745; font-weight: bold; }";
echo ".status-warning { color: #ffc107; font-weight: bold; }";
echo ".status-error { color: #dc3545; font-weight: bold; }";
echo ".metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; padding: 20px; margin: 10px 0; }";
echo ".issue-card { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0; }";
echo ".success-card { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0; }";
echo "</style>";
echo "</head><body class='container-fluid'>";

echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>🔍 Final Admin Logs System Audit Report</h1>";
echo "<p class='text-center text-muted'>Complete assessment of logging infrastructure and monitoring capabilities</p>";

$issues_found = [];
$recommendations = [];
$system_health = 'EXCELLENT';

try {
    $logger = getLogger();
    
    // 1. Database Infrastructure Audit
    echo "<div class='audit-card'>";
    echo "<h2>📊 Database Infrastructure Status</h2>";
    
    $required_tables = [
        'system_logs', 'security_logs', 'audit_logs', 'application_logs', 
        'error_logs', 'login_attempts', 'anomaly_logs', 'access_logs',
        'file_operations_logs', 'suspicious_activity_logs', 'admin_actions_logs', 'performance_logs'
    ];
    
    $table_status = [];
    foreach ($required_tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count, MAX(created_at) as latest FROM $table");
            $result = $stmt->fetch();
            $table_status[$table] = [
                'exists' => true,
                'count' => $result['count'],
                'latest' => $result['latest']
            ];
        } catch (Exception $e) {
            $table_status[$table] = ['exists' => false, 'error' => $e->getMessage()];
            $issues_found[] = "Table '$table' is missing or inaccessible";
        }
    }
    
    $tables_working = count(array_filter($table_status, function($s) { return $s['exists']; }));
    $tables_total = count($required_tables);
    
    echo "<div class='metric-card'>";
    echo "<h4>Database Tables: $tables_working/$tables_total Operational</h4>";
    echo "<p>Infrastructure Status: " . ($tables_working == $tables_total ? "✅ All Systems Operational" : "⚠️ Issues Detected") . "</p>";
    echo "</div>";
    echo "</div>";
    
    // 2. Recent Activity Analysis
    echo "<div class='audit-card'>";
    echo "<h2>📈 Recent Activity Analysis (Last 24 Hours)</h2>";
    
    $activity_data = [];
    foreach ($table_status as $table => $status) {
        if ($status['exists']) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
                $count_24h = $stmt->fetchColumn();
                $activity_data[$table] = $count_24h;
            } catch (Exception $e) {
                $activity_data[$table] = 0;
            }
        }
    }
    
    $total_activity = array_sum($activity_data);
    
    echo "<div class='row'>";
    echo "<div class='col-md-4'><div class='metric-card text-center'>";
    echo "<h3>$total_activity</h3><p>Total Log Entries (24h)</p></div></div>";
    
    $active_tables = count(array_filter($activity_data, function($count) { return $count > 0; }));
    echo "<div class='col-md-4'><div class='metric-card text-center'>";
    echo "<h3>$active_tables</h3><p>Active Log Tables</p></div></div>";
    
    $security_events = $activity_data['security_logs'] ?? 0;
    echo "<div class='col-md-4'><div class='metric-card text-center'>";
    echo "<h3>$security_events</h3><p>Security Events (24h)</p></div></div>";
    echo "</div>";
    
    // Show top active tables
    arsort($activity_data);
    echo "<h4>Most Active Log Tables:</h4>";
    echo "<table class='table table-striped'>";
    echo "<tr><th>Table</th><th>Records (24h)</th><th>Status</th></tr>";
    
    foreach (array_slice($activity_data, 0, 8, true) as $table => $count) {
        echo "<tr>";
        echo "<td><strong>$table</strong></td>";
        echo "<td>$count</td>";
        
        if ($count > 0) {
            echo "<td class='status-good'>✅ Active</td>";
        } elseif ($table == 'login_attempts' || $table == 'security_logs') {
            echo "<td class='status-warning'>⚠️ Low Activity</td>";
            if ($count == 0) {
                $recommendations[] = "Monitor $table - no activity in 24h may indicate logging issues";
            }
        } else {
            echo "<td class='text-muted'>⚪ Quiet</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // 3. Login Attempts Deep Dive
    echo "<div class='audit-card'>";
    echo "<h2>🔐 Login Attempts Analysis</h2>";
    
    try {
        // Get login statistics
        $stmt = $pdo->query("
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN success = 1 THEN 1 END) as successful,
                COUNT(CASE WHEN success = 0 THEN 1 END) as failed,
                COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as with_user_id,
                COUNT(CASE WHEN user_id IS NULL THEN 1 END) as without_user_id
            FROM login_attempts 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $login_stats = $stmt->fetch();
        
        echo "<div class='row'>";
        echo "<div class='col-md-3'><div class='metric-card text-center'>";
        echo "<h4>{$login_stats['total']}</h4><p>Total Attempts</p></div></div>";
        echo "<div class='col-md-3'><div class='metric-card text-center'>";
        echo "<h4>{$login_stats['successful']}</h4><p>Successful</p></div></div>";
        echo "<div class='col-md-3'><div class='metric-card text-center'>";
        echo "<h4>{$login_stats['failed']}</h4><p>Failed</p></div></div>";
        echo "<div class='col-md-3'><div class='metric-card text-center'>";
        echo "<h4>{$login_stats['with_user_id']}</h4><p>With User ID</p></div></div>";
        echo "</div>";
        
        // Check for issues
        if ($login_stats['with_user_id'] == 0 && $login_stats['total'] > 0) {
            $issues_found[] = "CRITICAL: No login attempts have user_id - logging system not properly associating users";
            $system_health = 'CRITICAL';
        }
        
        if ($login_stats['failed'] > $login_stats['successful'] * 2) {
            $issues_found[] = "WARNING: High failure rate detected - possible brute force attacks";
            $system_health = 'WARNING';
        }
        
        // Show recent failed attempts
        echo "<h4>Recent Failed Login Attempts:</h4>";
        $stmt = $pdo->query("
            SELECT email, user_id, failure_reason, ip_address, created_at 
            FROM login_attempts 
            WHERE success = 0 AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $failed_attempts = $stmt->fetchAll();
        
        if ($failed_attempts) {
            echo "<table class='table table-sm'>";
            echo "<tr><th>Email</th><th>User ID</th><th>Reason</th><th>IP</th><th>Time</th></tr>";
            foreach ($failed_attempts as $attempt) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($attempt['email']) . "</td>";
                echo "<td>" . ($attempt['user_id'] ?? '<span class="text-muted">NULL</span>') . "</td>";
                echo "<td>" . htmlspecialchars($attempt['failure_reason'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($attempt['ip_address']) . "</td>";
                echo "<td>" . date('M j, g:i A', strtotime($attempt['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='text-muted'>No failed login attempts in the last 24 hours.</p>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Error analyzing login attempts: " . $e->getMessage() . "</div>";
        $issues_found[] = "Cannot analyze login attempts: " . $e->getMessage();
    }
    echo "</div>";
    
    // 4. Admin Interface Testing
    echo "<div class='audit-card'>";
    echo "<h2>🖥️ Admin Interface Functionality</h2>";
    
    $admin_files = [
        'logs.php' => 'Main logs dashboard',
        'get_logs.php' => 'AJAX logs retrieval',
        'dashboard.php' => 'Admin dashboard'
    ];
    
    echo "<table class='table'>";
    echo "<tr><th>Component</th><th>Status</th><th>Action</th></tr>";
    
    foreach ($admin_files as $file => $description) {
        echo "<tr>";
        echo "<td><strong>$file</strong><br><small class='text-muted'>$description</small></td>";
        
        if (file_exists($file)) {
            echo "<td class='status-good'>✅ Available</td>";
            echo "<td><a href='$file' target='_blank' class='btn btn-sm btn-primary'>Test</a></td>";
        } else {
            echo "<td class='status-error'>❌ Missing</td>";
            echo "<td>-</td>";
            $issues_found[] = "Admin file '$file' is missing";
        }
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // 5. System Health Summary
    echo "<div class='audit-card'>";
    echo "<h2>🏥 System Health Summary</h2>";
    
    $health_color = $system_health == 'EXCELLENT' ? 'success' : ($system_health == 'WARNING' ? 'warning' : 'danger');
    echo "<div class='alert alert-$health_color'>";
    echo "<h4>Overall System Health: $system_health</h4>";
    echo "</div>";
    
    if (empty($issues_found)) {
        echo "<div class='success-card'>";
        echo "<h4>✅ All Systems Operational</h4>";
        echo "<p>Your admin logging system is fully functional and ready for production monitoring.</p>";
        echo "<ul>";
        echo "<li>All required database tables are present and accessible</li>";
        echo "<li>Login attempt logging is working correctly</li>";
        echo "<li>Admin interface components are available</li>";
        echo "<li>Recent activity indicates normal system operation</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='issue-card'>";
        echo "<h4>⚠️ Issues Requiring Attention</h4>";
        echo "<ul>";
        foreach ($issues_found as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($recommendations)) {
        echo "<div class='issue-card'>";
        echo "<h4>💡 Recommendations</h4>";
        echo "<ul>";
        foreach ($recommendations as $rec) {
            echo "<li>$rec</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    echo "</div>";
    
    // 6. Monitoring Checklist
    echo "<div class='audit-card'>";
    echo "<h2>📋 Daily Monitoring Checklist</h2>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<h5>🔒 Security Monitoring</h5>";
    echo "<ul>";
    echo "<li>Review failed login attempts</li>";
    echo "<li>Check for high-risk security events</li>";
    echo "<li>Monitor brute force protection triggers</li>";
    echo "<li>Verify user account lockouts</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<h5>📊 System Monitoring</h5>";
    echo "<ul>";
    echo "<li>Check application error rates</li>";
    echo "<li>Monitor system performance logs</li>";
    echo "<li>Review audit trail for data changes</li>";
    echo "<li>Verify log table growth rates</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='audit-card'>";
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Audit Failed</h4>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    echo "</div>";
}

echo "<div class='text-center mt-4 mb-4'>";
echo "<a href='logs.php' class='btn btn-primary btn-lg me-3'>📊 Open Admin Logs</a>";
echo "<a href='test_admin_logs_functionality.php' class='btn btn-info btn-lg me-3'>🧪 Run Functionality Test</a>";
echo "<a href='comprehensive_logs_audit.php' class='btn btn-secondary btn-lg'>🔍 Detailed Audit</a>";
echo "</div>";

echo "</div></body></html>";
?>
