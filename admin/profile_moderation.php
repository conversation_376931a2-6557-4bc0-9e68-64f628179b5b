<?php
session_start();
require '../config/db_connect.php';
require '../config/upload_config.php';
require '../utils/admin_profile_manager.php';

// Set page variables
$page_title = 'Profile Picture Moderation';
$page_subtitle = 'Review and manage user profile pictures';
$current_page = 'profile_moderation';

// Initialize profile manager
$profile_manager = new AdminProfileManager($pdo, $_SESSION['user_id']);

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'remove_single':
                $user_id = (int)$_POST['user_id'];
                $reason = $_POST['reason'] ?? 'Inappropriate content';
                try {
                    $profile_manager->removeUserProfilePicture($user_id, $reason);
                    $_SESSION['success_message'] = "Profile picture removed successfully.";
                } catch (Exception $e) {
                    $_SESSION['error_message'] = "Failed to remove profile picture: " . $e->getMessage();
                }
                break;
                
            case 'bulk_remove':
                $user_ids = $_POST['user_ids'] ?? [];
                $reason = $_POST['bulk_reason'] ?? 'Bulk moderation action';
                if (!empty($user_ids)) {
                    $result = $profile_manager->bulkRemoveProfilePictures($user_ids, $reason);
                    $_SESSION['success_message'] = "Removed {$result['removed_count']} profile pictures.";
                    if (!empty($result['errors'])) {
                        $_SESSION['error_message'] = "Some errors occurred: " . implode(', ', $result['errors']);
                    }
                } else {
                    $_SESSION['error_message'] = "No users selected for bulk action.";
                }
                break;
        }
        header("Location: profile_moderation.php");
        exit;
    }
}

// Pagination
$page = (int)($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Get users with custom pictures
$users = $profile_manager->getUsersWithCustomPictures($per_page, $offset);
$total_users = $profile_manager->getCustomPictureCount();
$total_pages = ceil($total_users / $per_page);

// Get moderation stats
$stats = $profile_manager->getModerationStats();

// Get recent action history
$recent_actions = $profile_manager->getProfilePictureActionHistory(10);

include 'includes/admin_header.php';
?>

<style>
    :root {
        /* Match Admin Panel Color Scheme */
        --primary-color: #2563eb;
        --primary-dark: #1d4ed8;
        --primary-light: #3b82f6;
        --secondary-color: #64748b;
        --accent-color: #0ea5e9;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --info-color: #0891b2;

        /* Neutral Colors */
        --gray-50: #f8fafc;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;

        /* Design System */
        --border-radius: 8px;
        --border-radius-lg: 12px;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --transition: all 0.2s ease-in-out;
    }

    /* Enhanced Page Header */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 32px;
        border-radius: var(--border-radius-lg);
        margin-bottom: 32px;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .page-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 8px 0;
        position: relative;
        z-index: 1;
    }

    .page-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    /* Enhanced Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
        margin-bottom: 32px;
    }

    .stat-card {
        background: white;
        padding: 28px;
        border-radius: var(--border-radius-lg);
        border: 1px solid var(--gray-200);
        text-align: center;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

    .stat-icon {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        font-size: 24px;
        color: white;
    }

    .stat-icon.custom {
        background: linear-gradient(135deg, var(--warning-color), #ea580c);
    }

    .stat-icon.default {
        background: linear-gradient(135deg, var(--info-color), var(--accent-color));
    }

    .stat-icon.removed {
        background: linear-gradient(135deg, var(--success-color), #16a34a);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        color: var(--gray-900);
        margin-bottom: 8px;
        line-height: 1;
    }

    .stat-label {
        font-size: 0.95rem;
        color: var(--gray-600);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Enhanced Bulk Actions */
    .bulk-actions {
        background: white;
        padding: 24px;
        border-radius: var(--border-radius-lg);
        margin-bottom: 32px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
    }

    .bulk-actions h5 {
        color: var(--gray-900);
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .bulk-actions .btn {
        padding: 12px 24px;
        font-weight: 500;
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .bulk-actions .btn-warning {
        background: var(--warning-color);
        border-color: var(--warning-color);
        color: white;
    }

    .bulk-actions .btn-warning:hover {
        background: #d97706;
        border-color: #d97706;
        transform: translateY(-1px);
    }

    .bulk-actions .btn-secondary {
        background: var(--gray-600);
        border-color: var(--gray-600);
        color: white;
    }

    .bulk-actions .btn-secondary:hover {
        background: var(--gray-700);
        border-color: var(--gray-700);
        transform: translateY(-1px);
    }

    /* Enhanced Moderation Cards */
    .moderation-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 32px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        margin-bottom: 32px;
        transition: var(--transition);
    }

    .moderation-card:hover {
        box-shadow: var(--shadow-md);
    }

    .moderation-card h3 {
        color: var(--gray-900);
        font-weight: 600;
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    /* Enhanced User Cards */
    .user-card {
        background: white;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius-lg);
        padding: 20px;
        margin-bottom: 20px;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .user-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-color);
        transform: scaleY(0);
        transition: var(--transition);
    }

    .user-card:hover {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .user-card:hover::before {
        transform: scaleY(1);
    }

    .user-card.selected {
        border-color: var(--success-color);
        background: rgba(16, 185, 129, 0.02);
    }

    .user-card.selected::before {
        background: var(--success-color);
        transform: scaleY(1);
    }

    /* Enhanced Profile Preview */
    .profile-preview {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid var(--gray-200);
        transition: var(--transition);
        box-shadow: var(--shadow-sm);
    }

    .user-card:hover .profile-preview {
        border-color: var(--primary-color);
        transform: scale(1.05);
    }

    .user-info h6 {
        color: var(--gray-900);
        font-weight: 600;
        margin-bottom: 4px;
    }

    .user-info small {
        color: var(--gray-500);
    }

    /* Enhanced Action Buttons */
    .btn-outline-warning {
        border: 2px solid var(--warning-color);
        color: var(--warning-color);
        background: transparent;
        transition: var(--transition);
    }

    .btn-outline-warning:hover {
        background: var(--warning-color);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    /* Enhanced Form Controls */
    .form-select, .form-control {
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        padding: 12px 16px;
        transition: var(--transition);
    }

    .form-select:focus, .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    /* Enhanced Badges */
    .badge {
        padding: 8px 12px;
        font-weight: 500;
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .badge.bg-info {
        background: var(--info-color) !important;
    }

    /* Enhanced Empty State */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--gray-500);
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 24px;
        color: var(--gray-300);
    }

    .empty-state h5 {
        color: var(--gray-600);
        margin-bottom: 12px;
    }

    /* Enhanced Table */
    .table-responsive {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table {
        margin-bottom: 0;
    }

    .table th {
        background: var(--gray-50);
        border-bottom: 2px solid var(--gray-200);
        color: var(--gray-700);
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 16px;
    }

    .table td {
        padding: 16px;
        vertical-align: middle;
        border-bottom: 1px solid var(--gray-100);
    }

    .table tbody tr:hover {
        background: var(--gray-50);
    }

    /* Enhanced Pagination */
    .pagination {
        margin-top: 32px;
    }

    .page-link {
        border: 2px solid var(--gray-200);
        color: var(--gray-600);
        padding: 12px 16px;
        margin: 0 4px;
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .page-link:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        background: rgba(37, 99, 235, 0.1);
    }

    .page-item.active .page-link {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    /* Enhanced Checkbox */
    .form-check-input {
        width: 20px;
        height: 20px;
        border: 2px solid var(--gray-300);
        border-radius: 4px;
        transition: var(--transition);
    }

    .form-check-input:checked {
        background: var(--success-color);
        border-color: var(--success-color);
    }

    .form-check-input:focus {
        box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .user-card {
            padding: 16px;
        }

        .profile-preview {
            width: 60px;
            height: 60px;
        }

        .page-header {
            padding: 24px;
        }

        .page-header h1 {
            font-size: 2rem;
        }
    }
</style>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-images me-3"></i>Profile Picture Moderation</h1>
    <p>Review and manage user profile pictures to maintain community standards</p>
</div>

<!-- Success/Error Messages -->
<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($_SESSION['success_message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($_SESSION['error_message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<!-- Enhanced Moderation Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon custom">
            <i class="fas fa-user-edit"></i>
        </div>
        <div class="stat-number"><?= number_format($stats['custom_pictures']) ?></div>
        <div class="stat-label">Custom Pictures</div>
    </div>
    <div class="stat-card">
        <div class="stat-icon default">
            <i class="fas fa-user-circle"></i>
        </div>
        <div class="stat-number"><?= number_format($stats['default_pictures']) ?></div>
        <div class="stat-label">Default Pictures</div>
    </div>
    <div class="stat-card">
        <div class="stat-icon removed">
            <i class="fas fa-shield-alt"></i>
        </div>
        <div class="stat-number"><?= number_format($stats['recent_removals']) ?></div>
        <div class="stat-label">Removed (30 days)</div>
    </div>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions">
    <h5><i class="fas fa-tasks me-2"></i>Bulk Actions</h5>
    <form method="POST" id="bulkForm">
        <input type="hidden" name="action" value="bulk_remove">
        <div class="row align-items-end">
            <div class="col-md-6">
                <label for="bulk_reason" class="form-label">Reason for removal</label>
                <select class="form-select" name="bulk_reason" id="bulk_reason">
                    <option value="Inappropriate content">Inappropriate content</option>
                    <option value="Violates community guidelines">Violates community guidelines</option>
                    <option value="Spam or promotional">Spam or promotional</option>
                    <option value="Copyright violation">Copyright violation</option>
                    <option value="Other policy violation">Other policy violation</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                    <i class="fas fa-trash-alt me-2"></i>Remove Selected
                </button>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-secondary" onclick="selectAll()">
                    <i class="fas fa-check-square me-2"></i>Select All
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Users with Custom Pictures -->
<div class="moderation-card">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-images me-2"></i>Users with Custom Profile Pictures</h3>
        <span class="badge bg-info"><?= number_format($total_users) ?> total</span>
    </div>
    
    <?php if (empty($users)): ?>
        <div class="empty-state">
            <i class="fas fa-images"></i>
            <h5>No custom profile pictures found</h5>
            <p>All users are using the default profile picture. This is great for maintaining consistency!</p>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($users as $user): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="user-card">
                        <div class="d-flex align-items-center mb-3">
                            <input type="checkbox" class="form-check-input me-3" name="user_ids[]"
                                   value="<?= $user['id'] ?>" form="bulkForm"
                                   onchange="toggleCardSelection(this)">
                            <img src="../<?= htmlspecialchars($user['profile_picture']) ?>"
                                 alt="<?= htmlspecialchars($user['username']) ?>"
                                 class="profile-preview me-3"
                                 onerror="this.src='../<?= DEFAULT_PROFILE_PICTURE ?>'">
                            <div class="user-info flex-grow-1">
                                <h6 class="mb-1"><?= htmlspecialchars($user['username']) ?></h6>
                                <small class="text-muted d-block"><?= htmlspecialchars($user['email']) ?></small>
                                <small class="text-info">
                                    <i class="fas fa-trophy me-1"></i>
                                    <?= $user['total_completions'] ?> completions
                                </small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex flex-column">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Joined <?= date('M j, Y', strtotime($user['created_at'])) ?>
                                </small>
                                <small class="text-warning mt-1">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Requires Review
                                </small>
                            </div>
                            <button class="btn btn-sm btn-outline-warning"
                                    onclick="removeProfilePicture(<?= $user['id'] ?>, '<?= htmlspecialchars($user['username']) ?>')"
                                    title="Remove Profile Picture">
                                <i class="fas fa-trash-alt me-1"></i>
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Profile moderation pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- Recent Actions -->
<div class="moderation-card">
    <h4><i class="fas fa-history me-2"></i>Recent Moderation Actions</h4>
    <?php if (empty($recent_actions)): ?>
        <p class="text-muted">No recent moderation actions.</p>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Admin</th>
                        <th>Target User</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_actions as $action): ?>
                        <tr>
                            <td><?= date('M j, Y g:i A', strtotime($action['created_at'])) ?></td>
                            <td><?= htmlspecialchars($action['admin_username']) ?></td>
                            <td><?= htmlspecialchars($action['target_username']) ?></td>
                            <td><?= htmlspecialchars($action['details']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Remove Profile Picture Modal -->
<div class="modal fade" id="removeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Profile Picture</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="remove_single">
                    <input type="hidden" name="user_id" id="removeUserId">
                    
                    <p>Are you sure you want to remove the profile picture for <strong id="removeUsername"></strong>?</p>
                    
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason for removal</label>
                        <select class="form-select" name="reason" required>
                            <option value="Inappropriate content">Inappropriate content</option>
                            <option value="Violates community guidelines">Violates community guidelines</option>
                            <option value="Spam or promotional">Spam or promotional</option>
                            <option value="Copyright violation">Copyright violation</option>
                            <option value="Other policy violation">Other policy violation</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Remove Picture</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function removeProfilePicture(userId, username) {
    document.getElementById('removeUserId').value = userId;
    document.getElementById('removeUsername').textContent = username;
    new bootstrap.Modal(document.getElementById('removeModal')).show();
}

function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
    const selectAllBtn = event.target;
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
        toggleCardSelection(cb);
    });

    // Update button text
    selectAllBtn.innerHTML = allChecked ?
        '<i class="fas fa-check-square me-2"></i>Select All' :
        '<i class="fas fa-square me-2"></i>Deselect All';
}

function toggleCardSelection(checkbox) {
    const card = checkbox.closest('.user-card');
    if (checkbox.checked) {
        card.classList.add('selected');
    } else {
        card.classList.remove('selected');
    }

    // Update select all button text
    updateSelectAllButton();
}

function updateSelectAllButton() {
    const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
    const selectAllBtn = document.querySelector('button[onclick="selectAll()"]');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    const someChecked = Array.from(checkboxes).some(cb => cb.checked);

    if (selectAllBtn) {
        if (allChecked && checkboxes.length > 0) {
            selectAllBtn.innerHTML = '<i class="fas fa-square me-2"></i>Deselect All';
            selectAllBtn.className = 'btn btn-outline-danger';
        } else {
            selectAllBtn.innerHTML = '<i class="fas fa-check-square me-2"></i>Select All';
            selectAllBtn.className = 'btn btn-secondary';
        }
    }
}

function confirmBulkAction() {
    const selected = document.querySelectorAll('input[name="user_ids[]"]:checked');
    if (selected.length === 0) {
        // Show a more user-friendly alert
        const toast = document.createElement('div');
        toast.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            Please select at least one user to perform bulk action.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);

        return false;
    }

    // Enhanced confirmation dialog
    const usernames = Array.from(selected).map(cb => {
        const card = cb.closest('.user-card');
        return card.querySelector('h6').textContent;
    });

    const message = selected.length === 1 ?
        `Are you sure you want to remove the profile picture for "${usernames[0]}"?` :
        `Are you sure you want to remove profile pictures for ${selected.length} users?\n\nUsers: ${usernames.slice(0, 3).join(', ')}${usernames.length > 3 ? ` and ${usernames.length - 3} more` : ''}`;

    return confirm(message);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Update select all button on page load
    updateSelectAllButton();

    // Add smooth scrolling to pagination
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.getAttribute('href') && this.getAttribute('href').includes('?page=')) {
                // Smooth scroll to top of moderation section
                const moderationCard = document.querySelector('.moderation-card');
                if (moderationCard) {
                    moderationCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }
        });
    });
});
</script>

<?php include 'includes/admin_footer.php'; ?>
