<?php
session_start();
require '../config/db_connect.php';
require '../config/brute_force_protection.php';

// Simple test script to verify brute force data retrieval
echo "<h1>Brute Force Data Retrieval Test</h1>";

$bruteForceProtection = new BruteForceProtection($pdo);

echo "<h2>1. Testing Database Connection</h2>";
try {
    $pdo->query("SELECT 1");
    echo "✅ Database connection: OK<br>";
} catch (Exception $e) {
    echo "❌ Database connection: FAILED - " . $e->getMessage() . "<br>";
}

echo "<h2>2. Testing Table Existence</h2>";
$tables = ['account_lockouts', 'brute_force_attempts'];
foreach ($tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "✅ Table '$table': EXISTS<br>";
            
            // Check table structure
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "&nbsp;&nbsp;&nbsp;Columns: " . implode(', ', $columns) . "<br>";
        } else {
            echo "❌ Table '$table': NOT FOUND<br>";
        }
    } catch (Exception $e) {
        echo "❌ Table '$table': ERROR - " . $e->getMessage() . "<br>";
    }
}

echo "<h2>3. Testing Data Queries</h2>";

// Test current lockouts query
try {
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM account_lockouts
        WHERE unlock_at > NOW()
    ");
    $count = $stmt->fetchColumn();
    echo "✅ Current locked accounts query: OK (found $count records)<br>";
} catch (Exception $e) {
    echo "❌ Current locked accounts query: FAILED - " . $e->getMessage() . "<br>";
}

// Test lockout history query
try {
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM account_lockouts
        WHERE locked_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $count = $stmt->fetchColumn();
    echo "✅ Lockout history query: OK (found $count records)<br>";
} catch (Exception $e) {
    echo "❌ Lockout history query: FAILED - " . $e->getMessage() . "<br>";
}

// Test recent attempts query
try {
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM brute_force_attempts
        WHERE attempt_time >= DATE_SUB(NOW(), INTERVAL 6 HOUR)
    ");
    $count = $stmt->fetchColumn();
    echo "✅ Recent attempts query: OK (found $count records)<br>";
} catch (Exception $e) {
    echo "❌ Recent attempts query: FAILED - " . $e->getMessage() . "<br>";
}

echo "<h2>4. Testing BruteForceProtection Class</h2>";
try {
    $stats = $bruteForceProtection->getLockoutStats();
    echo "✅ getLockoutStats(): OK<br>";
    echo "&nbsp;&nbsp;&nbsp;Current locked: " . ($stats['current_locked'] ?? 'N/A') . "<br>";
    echo "&nbsp;&nbsp;&nbsp;Recent attempts: " . ($stats['recent_attempts'] ?? 'N/A') . "<br>";
    echo "&nbsp;&nbsp;&nbsp;Top targeted: " . count($stats['top_targeted'] ?? []) . " accounts<br>";
} catch (Exception $e) {
    echo "❌ getLockoutStats(): FAILED - " . $e->getMessage() . "<br>";
}

echo "<h2>5. Sample Data</h2>";

// Show some sample lockout data
try {
    $stmt = $pdo->query("
        SELECT email, locked_at, unlock_at, attempt_count,
               (unlock_at > NOW()) as is_active
        FROM account_lockouts
        ORDER BY locked_at DESC
        LIMIT 5
    ");
    $lockouts = $stmt->fetchAll();
    
    if (empty($lockouts)) {
        echo "ℹ️ No lockout records found<br>";
    } else {
        echo "✅ Sample lockout records:<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Email</th><th>Locked At</th><th>Unlock At</th><th>Attempts</th><th>Status</th></tr>";
        foreach ($lockouts as $lockout) {
            $status = $lockout['is_active'] ? 'Active' : 'Expired';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($lockout['email']) . "</td>";
            echo "<td>" . $lockout['locked_at'] . "</td>";
            echo "<td>" . $lockout['unlock_at'] . "</td>";
            echo "<td>" . $lockout['attempt_count'] . "</td>";
            echo "<td>" . $status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Sample data query: FAILED - " . $e->getMessage() . "<br>";
}

// Show some sample attempt data
try {
    $stmt = $pdo->query("
        SELECT email, ip_address, attempt_time, COUNT(*) as count
        FROM brute_force_attempts
        WHERE attempt_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY email, ip_address
        ORDER BY attempt_time DESC
        LIMIT 5
    ");
    $attempts = $stmt->fetchAll();
    
    if (empty($attempts)) {
        echo "ℹ️ No recent attempt records found<br>";
    } else {
        echo "✅ Sample attempt records:<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Email</th><th>IP Address</th><th>Last Attempt</th><th>Count</th></tr>";
        foreach ($attempts as $attempt) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($attempt['email']) . "</td>";
            echo "<td>" . htmlspecialchars($attempt['ip_address']) . "</td>";
            echo "<td>" . $attempt['attempt_time'] . "</td>";
            echo "<td>" . $attempt['count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Sample attempts query: FAILED - " . $e->getMessage() . "<br>";
}

echo "<h2>6. Create Test Data</h2>";
echo "<form method='POST'>";
echo "<button type='submit' name='create_test'>Create Test Lockout Data</button>";
echo "</form>";

if (isset($_POST['create_test'])) {
    try {
        $test_email = 'test-' . time() . '@example.com';
        
        // Create failed attempts
        for ($i = 0; $i < 5; $i++) {
            $bruteForceProtection->recordFailedAttempt($test_email, '127.0.0.1', 'Test User Agent');
        }
        
        echo "✅ Test data created for: " . htmlspecialchars($test_email) . "<br>";
        echo "<a href='brute_force_management.php'>View in Brute Force Management</a><br>";
        
    } catch (Exception $e) {
        echo "❌ Test data creation failed: " . $e->getMessage() . "<br>";
    }
}

echo "<br><br><a href='brute_force_management.php'>← Back to Brute Force Management</a>";
?>
