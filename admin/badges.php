<?php
session_start();
require '../config/db_connect.php';

// Verify database connection
if (!isset($pdo)) {
    die('Database connection failed. Please check your configuration.');
}

// Set page variables
$page_title = 'Badge Management';
$page_subtitle = 'Manage user badges and achievements';

// Handle badge actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'award_badge':
                try {
                    $user_id = (int)$_POST['user_id'];
                    $category_id = (int)$_POST['category_id'];

                    // Validate inputs
                    if ($user_id <= 0 || $category_id <= 0) {
                        $error_message = "Invalid user or category selected.";
                        break;
                    }

                    // Verify user exists and is a student
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE id = ? AND role = 'student'");
                    $stmt->execute([$user_id]);
                    if ($stmt->fetchColumn() == 0) {
                        $error_message = "Invalid user selected or user is not a student.";
                        break;
                    }

                    // Verify category exists
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories WHERE id = ?");
                    $stmt->execute([$category_id]);
                    if ($stmt->fetchColumn() == 0) {
                        $error_message = "Invalid category selected.";
                        break;
                    }

                    // Get the badge for this category
                    $stmt = $pdo->prepare("SELECT id FROM badges WHERE category_id = ? AND requirement_type = 'category_complete' LIMIT 1");
                    $stmt->execute([$category_id]);
                    $badge = $stmt->fetch();

                    if (!$badge) {
                        $error_message = "No badge found for this category.";
                        break;
                    }

                    $badge_id = $badge['id'];

                    // Check if badge already exists
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_badges WHERE user_id = ? AND badge_id = ?");
                    $stmt->execute([$user_id, $badge_id]);

                    if ($stmt->fetchColumn() > 0) {
                        $error_message = "Badge already awarded to this user for this category.";
                    } else {
                        // Award badge
                        $stmt = $pdo->prepare("
                            INSERT INTO user_badges (user_id, badge_id, category_id, earned_at)
                            VALUES (?, ?, ?, NOW())
                        ");
                        $stmt->execute([$user_id, $badge_id, $category_id]);

                        $success_message = "Badge awarded successfully!";
                    }
                } catch (PDOException $e) {
                    error_log("Database error in badge award: " . $e->getMessage());
                    $error_message = "Database error occurred while awarding badge.";
                } catch (Exception $e) {
                    error_log("Error awarding badge: " . $e->getMessage());
                    $error_message = "Error awarding badge: " . $e->getMessage();
                }
                break;

            case 'revoke_badge':
                try {
                    $badge_id = (int)$_POST['badge_id'];

                    $stmt = $pdo->prepare("DELETE FROM user_badges WHERE id = ?");
                    $stmt->execute([$badge_id]);

                    $success_message = "Badge revoked successfully!";
                } catch (Exception $e) {
                    $error_message = "Error revoking badge: " . $e->getMessage();
                }
                break;

            case 'bulk_revoke':
                try {
                    $badge_ids = $_POST['badge_ids'] ?? [];
                    if (!empty($badge_ids)) {
                        $placeholders = str_repeat('?,', count($badge_ids) - 1) . '?';
                        $stmt = $pdo->prepare("DELETE FROM user_badges WHERE id IN ($placeholders)");
                        $stmt->execute($badge_ids);
                        $success_message = count($badge_ids) . " badges revoked successfully!";
                    } else {
                        $error_message = "No badges selected for bulk action.";
                    }
                } catch (Exception $e) {
                    $error_message = "Error performing bulk revoke: " . $e->getMessage();
                }
                break;
        }
    }
}

// Get badge statistics - ONLY for category completion badges
try {
    $stmt = $pdo->query("
        SELECT
            COUNT(ub.id) as total_badges,
            COUNT(DISTINCT ub.user_id) as unique_recipients,
            COUNT(CASE WHEN DATE(ub.earned_at) = CURDATE() THEN 1 END) as earned_today,
            COUNT(CASE WHEN DATE(ub.earned_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as earned_this_week
        FROM user_badges ub
        JOIN badges b ON ub.badge_id = b.id
        WHERE b.requirement_type = 'category_complete'
    ");
    $badge_stats = $stmt->fetch();
} catch (PDOException $e) {
    error_log("Error fetching badge statistics: " . $e->getMessage());
    $badge_stats = ['total_badges' => 0, 'unique_recipients' => 0, 'earned_today' => 0, 'earned_this_week' => 0];
}

// Get category badge distribution - ONLY for category completion badges
try {
    $stmt = $pdo->query("
        SELECT
            cat.name as category_name,
            cat.icon as category_icon,
            COUNT(ub.id) as badge_count,
            COUNT(DISTINCT ub.user_id) as unique_users,
            -- Calculate actual completion rate
            (SELECT COUNT(*) FROM challenges WHERE category_id = cat.id) as total_challenges,
            (SELECT COUNT(DISTINCT up.user_id)
             FROM user_progress up
             JOIN challenges c ON up.challenge_id = c.id
             WHERE c.category_id = cat.id AND up.status = 'completed'
             GROUP BY up.user_id
             HAVING COUNT(DISTINCT up.challenge_id) = (SELECT COUNT(*) FROM challenges WHERE category_id = cat.id)
            ) as users_with_full_completion
        FROM categories cat
        LEFT JOIN user_badges ub ON cat.id = ub.category_id
        LEFT JOIN badges b ON ub.badge_id = b.id AND b.requirement_type = 'category_complete'
        GROUP BY cat.id, cat.name, cat.icon
        ORDER BY badge_count DESC
    ");
    $category_badges = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching category badge distribution: " . $e->getMessage());
    $category_badges = [];
}

// Get recent badge awards - ONLY for category completion badges
try {
    $stmt = $pdo->query("
        SELECT
            ub.*,
            u.username,
            u.email,
            cat.name as category_name,
            cat.icon as category_icon,
            b.name as badge_name,
            b.icon as badge_icon,
            b.requirement_type,
            -- Get completion stats for context
            (SELECT COUNT(*) FROM challenges WHERE category_id = cat.id) as total_challenges,
            (SELECT COUNT(*) FROM user_progress up
             JOIN challenges c ON up.challenge_id = c.id
             WHERE c.category_id = cat.id AND up.user_id = ub.user_id AND up.status = 'completed'
            ) as completed_challenges
        FROM user_badges ub
        JOIN users u ON ub.user_id = u.id
        JOIN badges b ON ub.badge_id = b.id
        LEFT JOIN categories cat ON ub.category_id = cat.id
        WHERE b.requirement_type = 'category_complete'
        ORDER BY ub.earned_at DESC
        LIMIT 20
    ");
    $recent_badges = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching recent badges: " . $e->getMessage());
    $recent_badges = [];
}

// Get top badge earners - ONLY for category completion badges
try {
    $stmt = $pdo->query("
        SELECT
            u.username,
            u.email,
            COUNT(ub.id) as total_badges,
            COUNT(DISTINCT ub.category_id) as categories_completed,
            MAX(ub.earned_at) as last_badge_earned,
            -- Calculate actual completion percentage
            ROUND((COUNT(DISTINCT ub.category_id) / (SELECT COUNT(*) FROM categories)) * 100, 1) as completion_percentage
        FROM users u
        JOIN user_badges ub ON u.id = ub.user_id
        JOIN badges b ON ub.badge_id = b.id
        WHERE b.requirement_type = 'category_complete'
        GROUP BY u.id, u.username, u.email
        HAVING total_badges > 0
        ORDER BY categories_completed DESC, total_badges DESC, last_badge_earned DESC
        LIMIT 10
    ");
    $top_earners = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching top earners: " . $e->getMessage());
    $top_earners = [];
}

// Get badge earning trends (last 30 days) - ONLY for category completion badges
try {
    $stmt = $pdo->query("
        SELECT
            DATE(ub.earned_at) as date,
            COUNT(ub.id) as badges_earned,
            COUNT(DISTINCT ub.user_id) as unique_users,
            COUNT(DISTINCT ub.category_id) as categories_completed
        FROM user_badges ub
        JOIN badges b ON ub.badge_id = b.id
        WHERE ub.earned_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        AND b.requirement_type = 'category_complete'
        GROUP BY DATE(ub.earned_at)
        ORDER BY date ASC
    ");
    $badge_trends = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching badge trends: " . $e->getMessage());
    $badge_trends = [];
}

// Get categories for the award badge modal
try {
    $stmt = $pdo->query("SELECT id, name, icon FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
    $categories = [];
}

// Badge system validation - check for inconsistencies
try {
    // Check for users who completed categories but don't have badges
    $stmt = $pdo->query("
        SELECT
            u.username,
            cat.name as category_name,
            COUNT(DISTINCT c.id) as total_challenges,
            COUNT(DISTINCT up.challenge_id) as completed_challenges
        FROM users u
        JOIN user_progress up ON u.id = up.user_id AND up.status = 'completed'
        JOIN challenges c ON up.challenge_id = c.id
        JOIN categories cat ON c.category_id = cat.id
        LEFT JOIN user_badges ub ON u.id = ub.user_id AND cat.id = ub.category_id
        LEFT JOIN badges b ON ub.badge_id = b.id AND b.requirement_type = 'category_complete'
        WHERE ub.id IS NULL
        GROUP BY u.id, cat.id, u.username, cat.name
        HAVING completed_challenges = total_challenges AND total_challenges > 0
        ORDER BY u.username, cat.name
    ");
    $missing_badges = $stmt->fetchAll();

    // Check for badges awarded without full category completion
    $stmt = $pdo->query("
        SELECT
            u.username,
            cat.name as category_name,
            ub.earned_at,
            COUNT(DISTINCT c.id) as total_challenges,
            COUNT(DISTINCT up.challenge_id) as completed_challenges
        FROM user_badges ub
        JOIN users u ON ub.user_id = u.id
        JOIN badges b ON ub.badge_id = b.id AND b.requirement_type = 'category_complete'
        JOIN categories cat ON ub.category_id = cat.id
        JOIN challenges c ON cat.id = c.category_id
        LEFT JOIN user_progress up ON u.id = up.user_id AND c.id = up.challenge_id AND up.status = 'completed'
        GROUP BY ub.id, u.username, cat.name, ub.earned_at
        HAVING completed_challenges < total_challenges
        ORDER BY ub.earned_at DESC
    ");
    $invalid_badges = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Error in badge validation: " . $e->getMessage());
    $missing_badges = [];
    $invalid_badges = [];
}

include 'includes/admin_header.php';
?>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= $success_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?= $error_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<style>
    .badge-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 24px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
        margin-bottom: 24px;
    }

    .badge-card:hover {
        box-shadow: var(--shadow-md);
    }

    .badge-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        margin-bottom: 16px;
    }

    .category-badge-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 20px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
        text-align: center;
        height: 100%;
    }

    .category-badge-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .category-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        margin: 0 auto 16px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: var(--border-radius-lg);
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        text-align: center;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 4px;
    }

    .stat-label {
        font-size: 12px;
        color: var(--gray-500);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .recent-badge-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--gray-100);
    }

    .recent-badge-item:last-child {
        border-bottom: none;
    }

    .badge-mini-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        margin-right: 12px;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--gray-400);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 12px;
        margin-right: 12px;
    }

    .leaderboard-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid var(--gray-100);
    }

    .leaderboard-item:last-child {
        border-bottom: none;
    }

    .leaderboard-rank {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
        margin-right: 12px;
    }

    .leaderboard-rank.gold {
        background: #ffd700;
        color: #000;
    }

    .leaderboard-rank.silver {
        background: #c0c0c0;
        color: #000;
    }

    .leaderboard-rank.bronze {
        background: #cd7f32;
        color: #fff;
    }

    .progress-ring {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: conic-gradient(var(--primary-color) 0deg, var(--gray-200) 0deg);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin: 0 auto 12px;
    }

    .progress-ring::before {
        content: '';
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: white;
        position: absolute;
    }

    .progress-text {
        position: relative;
        z-index: 1;
        font-weight: 600;
        font-size: 12px;
        color: var(--gray-900);
    }

    .chart-wrapper {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .chart-wrapper canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
    }
</style>

<!-- Badge System Validation Alerts -->
<?php if (!empty($missing_badges) || !empty($invalid_badges)): ?>
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <h5 class="alert-heading">
        <i class="fas fa-exclamation-triangle me-2"></i>Badge System Validation Issues Found
    </h5>

    <?php if (!empty($missing_badges)): ?>
        <div class="mb-3">
            <strong>Missing Badges (<?= count($missing_badges) ?>):</strong>
            <p class="mb-2">Users who completed entire categories but don't have badges:</p>
            <div class="small">
                <?php foreach (array_slice($missing_badges, 0, 5) as $missing): ?>
                    <div>• <strong><?= htmlspecialchars($missing['username']) ?></strong> - <?= htmlspecialchars($missing['category_name']) ?> (<?= $missing['completed_challenges'] ?>/<?= $missing['total_challenges'] ?> challenges)</div>
                <?php endforeach; ?>
                <?php if (count($missing_badges) > 5): ?>
                    <div class="text-muted">... and <?= count($missing_badges) - 5 ?> more</div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($invalid_badges)): ?>
        <div class="mb-3">
            <strong>Invalid Badges (<?= count($invalid_badges) ?>):</strong>
            <p class="mb-2">Badges awarded without full category completion:</p>
            <div class="small">
                <?php foreach (array_slice($invalid_badges, 0, 3) as $invalid): ?>
                    <div>• <strong><?= htmlspecialchars($invalid['username']) ?></strong> - <?= htmlspecialchars($invalid['category_name']) ?> (only <?= $invalid['completed_challenges'] ?>/<?= $invalid['total_challenges'] ?> challenges)</div>
                <?php endforeach; ?>
                <?php if (count($invalid_badges) > 3): ?>
                    <div class="text-muted">... and <?= count($invalid_badges) - 3 ?> more</div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="mt-3">
        <button class="btn btn-sm btn-outline-primary me-2" onclick="fixMissingBadges()">
            <i class="fas fa-tools me-1"></i>Auto-Fix Missing Badges
        </button>
        <button class="btn btn-sm btn-outline-warning" onclick="showValidationDetails()">
            <i class="fas fa-list me-1"></i>View All Issues
        </button>
    </div>

    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Statistics Overview -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value"><?= number_format($badge_stats['total_badges']) ?></div>
        <div class="stat-label">Category Completion Badges</div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?= number_format($badge_stats['unique_recipients']) ?></div>
        <div class="stat-label">Badge Recipients</div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?= number_format($badge_stats['earned_today']) ?></div>
        <div class="stat-label">Earned Today</div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?= number_format($badge_stats['earned_this_week']) ?></div>
        <div class="stat-label">This Week</div>
    </div>
</div>

<!-- Category Badge Overview -->
<div class="badge-card">
    <h2 class="h4 mb-4">
        <i class="fas fa-medal me-2"></i>
        Category Badge Overview
    </h2>

    <div class="row g-4">
        <?php foreach ($category_badges as $category): ?>
            <div class="col-lg-3 col-md-6">
                <div class="category-badge-card">
                    <div class="category-icon">
                        <i class="<?= htmlspecialchars($category['category_icon']) ?>"></i>
                    </div>
                    <h3 class="h6 mb-2"><?= htmlspecialchars($category['category_name']) ?></h3>
                    <div class="stat-value"><?= number_format($category['badge_count']) ?></div>
                    <div class="stat-label">Category Completion Badges</div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <?= number_format($category['unique_users']) ?> users completed
                        </small>
                        <?php if ($category['total_challenges'] > 0): ?>
                            <br><small class="text-info">
                                <?= $category['total_challenges'] ?> challenges in category
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Badge Trends Chart -->
<div class="badge-card">
    <h2 class="h4 mb-4">
        <i class="fas fa-chart-line me-2"></i>
        Badge Earning Trends (Last 30 Days)
    </h2>
    <div class="chart-wrapper">
        <canvas id="badgeTrendsChart"></canvas>
    </div>
</div>

<div class="row g-4">
    <!-- Recent Badge Awards -->
    <div class="col-lg-8">
        <div class="badge-card">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <h2 class="h4 mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Recent Badge Awards
                </h2>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshBadges()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>

            <div class="recent-badges" style="max-height: 400px; overflow-y: auto;">
                <?php if (empty($recent_badges)): ?>
                    <div class="text-center py-4 text-muted">
                        <i class="fas fa-medal fa-2x mb-3"></i>
                        <p>No recent badge awards</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($recent_badges as $badge): ?>
                        <div class="recent-badge-item">
                            <div class="badge-mini-icon">
                                <i class="<?= htmlspecialchars($badge['category_icon']) ?>"></i>
                            </div>
                            <div class="user-avatar">
                                <?= strtoupper(substr($badge['username'], 0, 2)) ?>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold"><?= htmlspecialchars($badge['username']) ?></div>
                                <div class="text-muted small">
                                    Completed <strong><?= htmlspecialchars($badge['category_name']) ?></strong> category
                                    <?php if ($badge['total_challenges'] > 0): ?>
                                        <br><span class="text-success">
                                            ✓ <?= $badge['completed_challenges'] ?>/<?= $badge['total_challenges'] ?> challenges
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="small text-muted">
                                    <?= date('M j, Y', strtotime($badge['earned_at'])) ?>
                                </div>
                                <div class="small text-muted">
                                    <?= date('g:i A', strtotime($badge['earned_at'])) ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Top Badge Earners -->
    <div class="col-lg-4">
        <div class="badge-card">
            <h2 class="h4 mb-4">
                <i class="fas fa-trophy me-2"></i>
                Top Badge Earners
            </h2>

            <div class="leaderboard">
                <?php foreach ($top_earners as $index => $earner): ?>
                    <div class="leaderboard-item">
                        <div class="d-flex align-items-center">
                            <div class="leaderboard-rank <?= $index === 0 ? 'gold' : ($index === 1 ? 'silver' : ($index === 2 ? 'bronze' : '')) ?>">
                                <?= $index + 1 ?>
                            </div>
                            <div>
                                <div class="fw-semibold"><?= htmlspecialchars($earner['username']) ?></div>
                                <small class="text-muted">
                                    <?= $earner['categories_completed'] ?> categories completed
                                    <?php if ($earner['completion_percentage'] > 0): ?>
                                        <br><span class="text-success"><?= $earner['completion_percentage'] ?>% overall progress</span>
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="fw-semibold"><?= $earner['total_badges'] ?></div>
                            <small class="text-muted">badges</small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Badge Management Actions -->
        <div class="badge-card">
            <h2 class="h4 mb-4">
                <i class="fas fa-tools me-2"></i>
                Badge Management
            </h2>

            <div class="d-grid gap-2">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#awardBadgeModal">
                    <i class="fas fa-plus me-2"></i>Award Badge
                </button>

                <div class="dropdown">
                    <button class="btn btn-outline-info dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>Export Data
                    </button>
                    <ul class="dropdown-menu w-100">
                        <li><h6 class="dropdown-header">Export Format</h6></li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="exportBadges('csv')">
                                <i class="fas fa-file-csv me-2 text-success"></i>
                                Export as CSV
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="exportBadges('pdf')">
                                <i class="fas fa-file-pdf me-2 text-danger"></i>
                                Export as PDF
                            </a>
                        </li>
                    </ul>
                </div>

                <button class="btn btn-outline-success" onclick="generateReport()">
                    <i class="fas fa-chart-bar me-2"></i>Generate PDF Report
                </button>

                <button class="btn btn-outline-warning" onclick="toggleBulkMode()">
                    <i class="fas fa-tasks me-2"></i>Bulk Actions Mode
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Award Badge Modal -->
<div class="modal fade" id="awardBadgeModal" tabindex="-1" aria-labelledby="awardBadgeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="awardBadgeModalLabel">
                    <i class="fas fa-award me-2"></i>Award Badge
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="awardBadgeForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="award_badge">

                    <div class="mb-3">
                        <label for="user_id" class="form-label">Select User</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="">Choose a user...</option>
                            <?php
                            try {
                                $stmt = $pdo->query("SELECT id, username, email FROM users WHERE role = 'student' ORDER BY username");
                                $users = $stmt->fetchAll();
                                foreach ($users as $user):
                                ?>
                                    <option value="<?= $user['id'] ?>">
                                        <?= htmlspecialchars($user['username']) ?> (<?= htmlspecialchars($user['email']) ?>)
                                    </option>
                                <?php
                                endforeach;
                            } catch (PDOException $e) {
                                error_log("Error fetching users for badge modal: " . $e->getMessage());
                                echo '<option value="">Error loading users</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="category_id" class="form-label">Select Category</label>
                        <select class="form-select" id="category_id" name="category_id" required>
                            <option value="">Choose a category...</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>">
                                    <i class="<?= htmlspecialchars($category['icon']) ?>"></i>
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> A badge will be automatically awarded to the selected user for completing the chosen category.
                        Duplicate badges for the same category will not be created.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-award me-2"></i>Award Badge
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Professional color palette
const chartColors = {
    primary: '#2563eb',
    primaryLight: 'rgba(37, 99, 235, 0.1)',
    success: '#059669',
    warning: '#d97706',
    info: '#0891b2'
};

// Badge trends chart
const trendsData = <?= json_encode($badge_trends) ?>;
const ctx = document.getElementById('badgeTrendsChart').getContext('2d');
new Chart(ctx, {
    type: 'line',
    data: {
        labels: trendsData.map(item => {
            const date = new Date(item.date);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }),
        datasets: [{
            label: 'Badges Earned',
            data: trendsData.map(item => item.badges_earned),
            borderColor: chartColors.primary,
            backgroundColor: chartColors.primaryLight,
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: chartColors.primary,
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 5,
            pointHoverRadius: 7,
            pointHoverBackgroundColor: chartColors.primary,
            pointHoverBorderColor: '#ffffff',
            pointHoverBorderWidth: 3
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: '#1e293b',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#e2e8f0',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: false,
                callbacks: {
                    title: function(context) {
                        return `Date: ${context[0].label}`;
                    },
                    label: function(context) {
                        return `Badges Earned: ${context.parsed.y}`;
                    }
                }
            }
        },
        scales: {
            x: {
                grid: {
                    color: '#f1f5f9',
                    borderColor: '#e2e8f0'
                },
                ticks: {
                    color: '#64748b',
                    font: {
                        size: 12,
                        family: 'Inter, sans-serif'
                    }
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: '#f1f5f9',
                    borderColor: '#e2e8f0'
                },
                ticks: {
                    stepSize: 1,
                    color: '#64748b',
                    font: {
                        size: 12,
                        family: 'Inter, sans-serif'
                    }
                }
            }
        }
    }
});

function refreshBadges() {
    showNotification('Refreshing badge data...', 'info');
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Badge validation and auto-fix functions
function fixMissingBadges() {
    if (!confirm('This will automatically award badges to users who have completed entire categories but are missing their badges. Continue?')) {
        return;
    }

    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Fixing...';
    btn.disabled = true;

    fetch('fix_missing_badges.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=fix_missing_badges'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Successfully awarded ${data.badges_awarded} missing badges to ${data.users_affected} users.`, 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showNotification('Error fixing badges: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        showNotification('Error: ' + error.message, 'danger');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function showValidationDetails() {
    // Create modal to show all validation issues
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-clipboard-check me-2"></i>Badge System Validation Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="validationContent">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                            <p>Loading validation details...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportValidationReport()">
                        <i class="fas fa-download me-1"></i>Export Report
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Load validation details
    fetch('get_validation_details.php')
        .then(response => response.text())
        .then(html => {
            document.getElementById('validationContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('validationContent').innerHTML =
                '<div class="alert alert-danger">Error loading validation details: ' + error.message + '</div>';
        });

    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

function exportValidationReport() {
    window.open('export_validation_report.php', '_blank');
}

function exportBadges(format = 'csv') {
    showNotification('Preparing export...', 'info');

    let exportUrl;
    let filename;

    if (format === 'pdf') {
        exportUrl = 'generate_badge_report.php?download=true';
        filename = 'badges_report_' + new Date().toISOString().split('T')[0] + '.pdf';
        showNotification('Generating PDF report...', 'info');

        // For PDF, open in new tab for print dialog
        const newWindow = window.open(exportUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        if (newWindow) {
            setTimeout(() => {
                showNotification('PDF report opened in new tab. The print dialog will appear automatically to save as PDF.', 'success');
            }, 1000);
        } else {
            showNotification('Please allow popups to export PDF. Check your browser\'s popup blocker.', 'warning');
        }
        return; // Exit early for PDF
    } else {
        exportUrl = 'export_badges.php';
        filename = 'badges_export_' + new Date().toISOString().split('T')[0] + '.csv';
    }

    try {
        console.log('Attempting to export from:', exportUrl);

        // Create invisible link and click it
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = filename;
        link.style.display = 'none';
        link.target = '_blank';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
            showNotification('Export started! Check your downloads folder.', 'success');
        }, 1000);

    } catch (error) {
        console.error('Export error:', error);
        showNotification('Export failed. Trying alternative method...', 'warning');

        // Fallback: Direct window.open
        try {
            window.open(exportUrl, '_blank');
            setTimeout(() => {
                showNotification('Export opened in new tab. Save the file manually if needed.', 'info');
            }, 1000);
        } catch (fallbackError) {
            console.error('Fallback export error:', fallbackError);
            showNotification('Export failed. Please contact administrator.', 'danger');
        }
    }
}

function generateReport() {
    showNotification('Generating comprehensive badge report...', 'info');

    const newWindow = window.open('generate_badge_report.php?download=true', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
    if (newWindow) {
        setTimeout(() => {
            showNotification('Badge report opened in new tab. The print dialog will appear automatically.', 'success');
        }, 1000);
    } else {
        showNotification('Please allow popups to generate badge report.', 'warning');
    }
}

function toggleBulkMode() {
    showNotification('Bulk actions mode for badges coming soon! This will allow you to revoke multiple badges at once.', 'info');
}
</script>

<?php include 'includes/admin_footer.php'; ?>
