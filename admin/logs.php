<?php
session_start();
require '../config/db_connect.php';

// Set page variables
$page_title = 'System Logs';
$page_subtitle = 'Monitor system activities, security events, and audit trails';

// Check if logging tables exist
$tables_exist = true;
$missing_tables = [];

$required_tables = ['system_logs', 'security_logs', 'audit_logs', 'error_logs', 'login_attempts', 'anomaly_logs'];
foreach ($required_tables as $table) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
    if ($stmt->rowCount() == 0) {
        $tables_exist = false;
        $missing_tables[] = $table;
    }
}

if (!$tables_exist) {
    // Redirect to setup page if tables don't exist
    header('Location: setup_logging.php');
    exit;
}

// Get log statistics
$log_stats = [];

try {
    // System logs stats
    $stmt = $pdo->query("SELECT log_level, COUNT(*) as count FROM system_logs GROUP BY log_level");
    $system_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // Security logs stats
    $stmt = $pdo->query("SELECT event_type, COUNT(*) as count FROM security_logs GROUP BY event_type ORDER BY count DESC LIMIT 5");
    $security_stats = $stmt->fetchAll();

    // Recent logs counts
    $stmt = $pdo->query("SELECT COUNT(*) FROM system_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $log_stats['system_24h'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM security_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $log_stats['security_24h'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM audit_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $log_stats['audit_24h'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM error_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $log_stats['error_24h'] = $stmt->fetchColumn();

    // Login attempts stats
    $stmt = $pdo->query("SELECT COUNT(*) FROM login_attempts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $log_stats['login_24h'] = $stmt->fetchColumn();

    // Anomaly logs stats
    $stmt = $pdo->query("SELECT COUNT(*) FROM anomaly_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $log_stats['anomaly_24h'] = $stmt->fetchColumn();

} catch (PDOException $e) {
    // If there's an error, redirect to setup
    header('Location: setup_logging.php');
    exit;
}

// Get recent critical events
$stmt = $pdo->query("
    SELECT 'SECURITY' as type, event_type as event, username, ip_address, created_at, risk_level as severity
    FROM security_logs 
    WHERE risk_level IN ('HIGH', 'CRITICAL')
    UNION ALL
    SELECT 'SYSTEM' as type, category as event, 'System' as username, ip_address, created_at, log_level as severity
    FROM system_logs 
    WHERE log_level IN ('ERROR', 'CRITICAL')
    ORDER BY created_at DESC 
    LIMIT 10
");
$critical_events = $stmt->fetchAll();

include 'includes/admin_header.php';
?>

<style>
    .log-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        border: 1px solid #e5e7eb;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .log-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .log-stat {
        text-align: center;
        padding: 20px;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        margin-bottom: 20px;
    }

    .log-stat.system { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); }
    .log-stat.security { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); }
    .log-stat.audit { background: linear-gradient(135deg, #059669 0%, #047857 100%); }
    .log-stat.error { background: linear-gradient(135deg, #d97706 0%, #b45309 100%); }

    .log-stat-number {
        font-size: 32px;
        font-weight: 800;
        margin-bottom: 8px;
    }

    .log-stat-label {
        font-size: 14px;
        opacity: 0.9;
    }

    .log-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .log-table table {
        margin: 0;
    }

    .log-table th {
        background: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #374151;
        padding: 16px;
    }

    .log-table td {
        padding: 12px 16px;
        border-top: 1px solid #e5e7eb;
        vertical-align: middle;
    }

    .log-level-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .log-level-info { background: #dbeafe; color: #1e40af; }
    .log-level-warning { background: #fef3c7; color: #92400e; }
    .log-level-error { background: #fecaca; color: #b91c1c; }
    .log-level-critical { background: #fca5a5; color: #7f1d1d; }

    .risk-level-low { background: #d1fae5; color: #065f46; }
    .risk-level-medium { background: #fed7aa; color: #9a3412; }
    .risk-level-high { background: #fecaca; color: #b91c1c; }

    .log-filters {
        background: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 24px;
        border: 1px solid #e5e7eb;
        border-left: 4px solid #6366f1;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .log-filters .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 6px;
    }

    .log-filters .form-control,
    .log-filters .form-select {
        border: 1px solid #d1d5db;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .log-filters .form-control:focus,
    .log-filters .form-select:focus {
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .alert-sm {
        padding: 8px 12px;
        font-size: 0.875rem;
    }

    #customDateRange {
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .tab-content {
        background: white;
        border-radius: 0 8px 8px 8px;
        border: 1px solid #e5e7eb;
        border-top: none;
    }

    .nav-tabs .nav-link {
        border: 1px solid #e5e7eb;
        border-bottom: none;
        background: #f8f9fa;
        color: #6b7280;
        font-weight: 500;
    }

    .nav-tabs .nav-link.active {
        background: white;
        color: #2563eb;
        border-color: #e5e7eb #e5e7eb white;
    }

    .critical-event {
        padding: 12px 16px;
        border-left: 4px solid #dc2626;
        background: #fef2f2;
        margin-bottom: 8px;
        border-radius: 0 6px 6px 0;
    }

    .critical-event.high { border-left-color: #f59e0b; background: #fffbeb; }
    .critical-event.medium { border-left-color: #3b82f6; background: #eff6ff; }
</style>

<!-- Log Statistics -->
<div class="row g-4 mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="log-stat system">
            <div class="log-stat-number"><?= number_format($log_stats['system_24h']) ?></div>
            <div class="log-stat-label">System Events (24h)</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="log-stat security">
            <div class="log-stat-number"><?= number_format($log_stats['security_24h']) ?></div>
            <div class="log-stat-label">Security Events (24h)</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="log-stat audit">
            <div class="log-stat-number"><?= number_format($log_stats['audit_24h']) ?></div>
            <div class="log-stat-label">Audit Events (24h)</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="log-stat error">
            <div class="log-stat-number"><?= number_format($log_stats['error_24h']) ?></div>
            <div class="log-stat-label">Errors (24h)</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="log-stat" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
            <div class="log-stat-number"><?= number_format($log_stats['login_24h']) ?></div>
            <div class="log-stat-label">Login Attempts (24h)</div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="log-stat" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="log-stat-number"><?= number_format($log_stats['anomaly_24h']) ?></div>
            <div class="log-stat-label">Anomalies (24h)</div>
        </div>
    </div>
</div>

<!-- Critical Events Alert -->
<?php if (!empty($critical_events)): ?>
<div class="log-card">
    <h4 class="mb-3">
        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
        Critical Events & Alerts
    </h4>
    <div class="critical-events">
        <?php foreach ($critical_events as $event): ?>
            <div class="critical-event <?= strtolower($event['severity']) ?>">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong><?= htmlspecialchars($event['type']) ?>:</strong>
                        <?= htmlspecialchars($event['event']) ?>
                        <span class="text-muted">by <?= htmlspecialchars($event['username']) ?></span>
                        <small class="text-muted">(<?= htmlspecialchars($event['ip_address']) ?>)</small>
                    </div>
                    <div class="text-end">
                        <span class="log-level-badge log-level-<?= strtolower($event['severity']) ?>">
                            <?= htmlspecialchars($event['severity']) ?>
                        </span>
                        <br>
                        <small class="text-muted"><?= date('M j, g:i A', strtotime($event['created_at'])) ?></small>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<!-- Log Filters -->
<div class="log-filters">
    <div class="row g-3">
        <div class="col-md-3">
            <label class="form-label">Date Range</label>
            <select class="form-select" id="dateRange" onchange="toggleCustomDateRange()">
                <option value="today">Today</option>
                <option value="week">Last 7 days</option>
                <option value="month" selected>Last 30 days</option>
                <option value="all">All Time</option>
                <option value="custom">Custom Range</option>
            </select>
            <!-- Custom Date Range -->
            <div id="customDateRange" class="mt-2" style="display: none;">
                <div class="row g-2">
                    <div class="col-6">
                        <input type="date" class="form-control form-control-sm" id="startDate" placeholder="Start Date">
                    </div>
                    <div class="col-6">
                        <input type="date" class="form-control form-control-sm" id="endDate" placeholder="End Date">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <label class="form-label">Log Level</label>
            <select class="form-select" id="logLevel">
                <option value="">All Levels</option>
                <option value="INFO">Info</option>
                <option value="WARNING">Warning</option>
                <option value="ERROR">Error</option>
                <option value="CRITICAL">Critical</option>
                <option value="HIGH">High</option>
                <option value="MEDIUM">Medium</option>
                <option value="LOW">Low</option>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">User</label>
            <input type="text" class="form-control" id="userFilter" placeholder="Filter by username">
        </div>
        <div class="col-md-2">
            <label class="form-label">IP Address</label>
            <input type="text" class="form-control" id="ipFilter" placeholder="Filter by IP">
        </div>
        <div class="col-md-3">
            <label class="form-label">Actions</label>
            <div class="d-flex gap-2">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-filter me-1"></i>Filter
                </button>
                <button class="btn btn-outline-secondary" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>Clear
                </button>
                <button class="btn btn-outline-success" onclick="exportLogs()">
                    <i class="fas fa-download me-1"></i>Export
                </button>
            </div>
        </div>
    </div>

    <!-- Filter Status -->
    <div id="filterStatus" class="mt-2" style="display: none;">
        <div class="alert alert-info alert-sm d-flex justify-content-between align-items-center">
            <span id="filterStatusText">Filters applied</span>
            <button type="button" class="btn-close btn-sm" onclick="clearFilters()"></button>
        </div>
    </div>
</div>

<!-- Log Tabs -->
<ul class="nav nav-tabs" id="logTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
            <i class="fas fa-server me-2"></i>System Logs
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
            <i class="fas fa-shield-alt me-2"></i>Security Logs
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="login-tab" data-bs-toggle="tab" data-bs-target="#login" type="button" role="tab">
            <i class="fas fa-sign-in-alt me-2"></i>Login Attempts
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="anomaly-tab" data-bs-toggle="tab" data-bs-target="#anomaly" type="button" role="tab">
            <i class="fas fa-exclamation-triangle me-2"></i>Anomalies
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="access-tab" data-bs-toggle="tab" data-bs-target="#access" type="button" role="tab">
            <i class="fas fa-folder-open me-2"></i>Access Logs
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="file_operations-tab" data-bs-toggle="tab" data-bs-target="#file_operations" type="button" role="tab">
            <i class="fas fa-file-alt me-2"></i>File Operations
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="suspicious-tab" data-bs-toggle="tab" data-bs-target="#suspicious" type="button" role="tab">
            <i class="fas fa-shield-alt me-2"></i>Suspicious Activity
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="admin_actions-tab" data-bs-toggle="tab" data-bs-target="#admin_actions" type="button" role="tab">
            <i class="fas fa-user-shield me-2"></i>Admin Actions
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
            <i class="fas fa-tachometer-alt me-2"></i>Performance
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="audit-tab" data-bs-toggle="tab" data-bs-target="#audit" type="button" role="tab">
            <i class="fas fa-clipboard-list me-2"></i>Audit Logs
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="application-tab" data-bs-toggle="tab" data-bs-target="#application" type="button" role="tab">
            <i class="fas fa-code me-2"></i>Application Logs
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="error-tab" data-bs-toggle="tab" data-bs-target="#error" type="button" role="tab">
            <i class="fas fa-exclamation-circle me-2"></i>Error Logs
        </button>
    </li>
</ul>

<div class="tab-content" id="logTabContent">
    <!-- System Logs Tab -->
    <div class="tab-pane fade show active" id="system" role="tabpanel">
        <div class="log-table">
            <div id="systemLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading system logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Logs Tab -->
    <div class="tab-pane fade" id="security" role="tabpanel">
        <div class="log-table">
            <div id="securityLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading security logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Attempts Tab -->
    <div class="tab-pane fade" id="login" role="tabpanel">
        <div class="log-table">
            <div id="loginLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading login attempts...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Anomaly Logs Tab -->
    <div class="tab-pane fade" id="anomaly" role="tabpanel">
        <div class="log-table">
            <div id="anomalyLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading anomaly logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Access Logs Tab -->
    <div class="tab-pane fade" id="access" role="tabpanel">
        <div class="log-table">
            <div id="accessLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading access logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- File Operations Tab -->
    <div class="tab-pane fade" id="file_operations" role="tabpanel">
        <div class="log-table">
            <div id="file_operationsLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading file operation logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Suspicious Activity Tab -->
    <div class="tab-pane fade" id="suspicious" role="tabpanel">
        <div class="log-table">
            <div id="suspiciousLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading suspicious activity logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Actions Tab -->
    <div class="tab-pane fade" id="admin_actions" role="tabpanel">
        <div class="log-table">
            <div id="admin_actionsLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading admin action logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Tab -->
    <div class="tab-pane fade" id="performance" role="tabpanel">
        <div class="log-table">
            <div id="performanceLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading performance logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit Logs Tab -->
    <div class="tab-pane fade" id="audit" role="tabpanel">
        <div class="log-table">
            <div id="auditLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading audit logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Logs Tab -->
    <div class="tab-pane fade" id="application" role="tabpanel">
        <div class="log-table">
            <div id="applicationLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading application logs...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Logs Tab -->
    <div class="tab-pane fade" id="error" role="tabpanel">
        <div class="log-table">
            <div id="errorLogsContent">
                <div class="text-center p-4">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                    <p>Loading error logs...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load logs when tab is clicked (handled in the main DOMContentLoaded below)

function loadLogs(type) {
    const contentDiv = document.getElementById(type + 'LogsContent');

    if (!contentDiv) {
        console.error('Content div not found for type:', type);
        return;
    }

    // Show loading state
    contentDiv.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x mb-3"></i><p>Loading ' + type + ' logs...</p></div>';

    // Use month as default date range for better results
    const dateRange = document.getElementById('dateRange') ? document.getElementById('dateRange').value : 'month';

    fetch(`get_logs.php?type=${type}&limit=50&date_range=${dateRange}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(data => {
            contentDiv.innerHTML = data;
        })
        .catch(error => {
            console.error('Error loading logs:', error);
            contentDiv.innerHTML = '<div class="alert alert-danger">Error loading logs: ' + error.message + '</div>';
        });
}

function toggleCustomDateRange() {
    const dateRange = document.getElementById('dateRange').value;
    const customDateRange = document.getElementById('customDateRange');

    if (dateRange === 'custom') {
        customDateRange.style.display = 'block';
        // Set default dates (last 7 days)
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);

        document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
        document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    } else {
        customDateRange.style.display = 'none';
    }
}

function applyFilters() {
    const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target').substring(1);
    const dateRange = document.getElementById('dateRange').value;
    const logLevel = document.getElementById('logLevel').value;
    const userFilter = document.getElementById('userFilter').value;
    const ipFilter = document.getElementById('ipFilter').value;

    const params = new URLSearchParams({
        type: activeTab,
        date_range: dateRange,
        log_level: logLevel,
        user_filter: userFilter,
        ip_filter: ipFilter,
        limit: 100
    });

    // Add custom date range if selected
    if (dateRange === 'custom') {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
    }

    const contentDiv = document.getElementById(activeTab + 'LogsContent');
    contentDiv.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x mb-3"></i><p>Applying filters...</p></div>';

    fetch(`get_logs.php?${params}`)
        .then(response => response.text())
        .then(data => {
            contentDiv.innerHTML = data;
            updateFilterStatus();
        })
        .catch(error => {
            contentDiv.innerHTML = '<div class="alert alert-danger">Error applying filters: ' + error.message + '</div>';
        });
}

function clearFilters() {
    // Reset all filter inputs
    document.getElementById('dateRange').value = 'month';
    document.getElementById('logLevel').value = '';
    document.getElementById('userFilter').value = '';
    document.getElementById('ipFilter').value = '';
    document.getElementById('customDateRange').style.display = 'none';

    // Hide filter status
    document.getElementById('filterStatus').style.display = 'none';

    // Reload current tab
    const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target').substring(1);
    loadLogs(activeTab);
}

function updateFilterStatus() {
    const dateRange = document.getElementById('dateRange').value;
    const logLevel = document.getElementById('logLevel').value;
    const userFilter = document.getElementById('userFilter').value;
    const ipFilter = document.getElementById('ipFilter').value;

    const filters = [];
    if (dateRange !== 'month') filters.push(`Date: ${dateRange}`);
    if (logLevel) filters.push(`Level: ${logLevel}`);
    if (userFilter) filters.push(`User: ${userFilter}`);
    if (ipFilter) filters.push(`IP: ${ipFilter}`);

    const filterStatus = document.getElementById('filterStatus');
    const filterStatusText = document.getElementById('filterStatusText');

    if (filters.length > 0) {
        filterStatusText.textContent = `Active filters: ${filters.join(', ')}`;
        filterStatus.style.display = 'block';
    } else {
        filterStatus.style.display = 'none';
    }
}

function exportLogs() {
    const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target').substring(1);
    const dateRange = document.getElementById('dateRange').value;
    const logLevel = document.getElementById('logLevel').value;
    const userFilter = document.getElementById('userFilter').value;
    const ipFilter = document.getElementById('ipFilter').value;

    const params = new URLSearchParams({
        type: activeTab,
        date_range: dateRange,
        log_level: logLevel,
        user_filter: userFilter,
        ip_filter: ipFilter,
        export: 'csv'
    });

    // Add custom date range if selected
    if (dateRange === 'custom') {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
    }

    window.open(`export_logs.php?${params}`, '_blank');
}

// Add auto-filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load system logs by default
    loadLogs('system');

    // Add event listeners for tab changes
    const tabs = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.getAttribute('data-bs-target').substring(1);
            loadLogs(target);
        });
    });

    // Add auto-filtering with debounce
    let filterTimeout;
    const autoFilterInputs = ['userFilter', 'ipFilter'];

    autoFilterInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', function() {
                clearTimeout(filterTimeout);
                filterTimeout = setTimeout(() => {
                    if (this.value.length >= 2 || this.value.length === 0) {
                        applyFilters();
                    }
                }, 500); // 500ms debounce
            });
        }
    });

    // Auto-filter on dropdown changes
    const autoFilterSelects = ['dateRange', 'logLevel'];
    autoFilterSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            select.addEventListener('change', function() {
                applyFilters();
            });
        }
    });

    // Auto-filter on custom date changes
    const dateInputs = ['startDate', 'endDate'];
    dateInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('change', function() {
                if (document.getElementById('dateRange').value === 'custom') {
                    applyFilters();
                }
            });
        }
    });
});

// Auto-refresh logs every 30 seconds
setInterval(function() {
    const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target').substring(1);
    loadLogs(activeTab);
}, 30000);

// Function to resolve anomaly
window.resolveAnomaly = function(anomalyId) {
    if (confirm('Are you sure you want to mark this anomaly as resolved?')) {
        fetch('resolve_anomaly.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ anomaly_id: anomalyId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadLogs('anomaly'); // Reload anomaly logs
                showAlert('Anomaly marked as resolved', 'success');
            } else {
                showAlert('Error resolving anomaly: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('Error resolving anomaly', 'danger');
        });
    }
};

// Function to show log details
window.showLogDetails = function(logId, logType) {
    fetch(`get_log_details.php?id=${logId}&type=${logType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Create modal to show details
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Log Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <pre>${JSON.stringify(data.details, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
                modal.addEventListener('hidden.bs.modal', () => modal.remove());
            }
        });
};

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>

<?php include 'includes/admin_footer.php'; ?>
