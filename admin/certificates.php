<?php
session_start();
require '../config/db_connect.php';

// Set page variables
$page_title = 'Certificate Management';
$page_subtitle = 'Manage user certificates and achievements';

// Handle certificate actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'revoke_certificate':
                try {
                    $stmt = $pdo->prepare("DELETE FROM user_certificates WHERE id = ?");
                    $stmt->execute([$_POST['certificate_id']]);
                    $success_message = "Certificate revoked successfully!";
                } catch (Exception $e) {
                    $error_message = "Error revoking certificate: " . $e->getMessage();
                }
                break;

            case 'regenerate_certificate':
                try {
                    $certificate_id = $_POST['certificate_id'];

                    // Get certificate details
                    $stmt = $pdo->prepare("
                        SELECT uc.*, u.username, cat.name as category_name
                        FROM user_certificates uc
                        JOIN users u ON uc.user_id = u.id
                        JOIN categories cat ON uc.category_id = cat.id
                        WHERE uc.id = ?
                    ");
                    $stmt->execute([$certificate_id]);
                    $cert = $stmt->fetch();

                    if ($cert) {
                        // Generate new certificate code
                        $new_code = 'TMO-' . strtoupper(substr(md5(uniqid()), 0, 8));

                        // Update certificate
                        $stmt = $pdo->prepare("
                            UPDATE user_certificates
                            SET certificate_code = ?, issued_at = NOW()
                            WHERE id = ?
                        ");
                        $stmt->execute([$new_code, $certificate_id]);

                        $success_message = "Certificate regenerated successfully!";
                    }
                } catch (Exception $e) {
                    $error_message = "Error regenerating certificate: " . $e->getMessage();
                }
                break;

            case 'bulk_revoke':
                try {
                    $certificate_ids = $_POST['certificate_ids'] ?? [];
                    if (!empty($certificate_ids)) {
                        $placeholders = str_repeat('?,', count($certificate_ids) - 1) . '?';
                        $stmt = $pdo->prepare("DELETE FROM user_certificates WHERE id IN ($placeholders)");
                        $stmt->execute($certificate_ids);
                        $success_message = count($certificate_ids) . " certificates revoked successfully!";
                    } else {
                        $error_message = "No certificates selected for bulk action.";
                    }
                } catch (Exception $e) {
                    $error_message = "Error performing bulk revoke: " . $e->getMessage();
                }
                break;

            case 'bulk_regenerate':
                try {
                    $certificate_ids = $_POST['certificate_ids'] ?? [];
                    if (!empty($certificate_ids)) {
                        $pdo->beginTransaction();

                        foreach ($certificate_ids as $cert_id) {
                            $new_code = 'TMO-' . strtoupper(substr(md5(uniqid()), 0, 8));
                            $stmt = $pdo->prepare("
                                UPDATE user_certificates
                                SET certificate_code = ?, issued_at = NOW()
                                WHERE id = ?
                            ");
                            $stmt->execute([$new_code, $cert_id]);
                        }

                        $pdo->commit();
                        $success_message = count($certificate_ids) . " certificates regenerated successfully!";
                    } else {
                        $error_message = "No certificates selected for bulk action.";
                    }
                } catch (Exception $e) {
                    $pdo->rollBack();
                    $error_message = "Error performing bulk regenerate: " . $e->getMessage();
                }
                break;

            case 'issue_certificate':
                try {
                    $user_id = (int)$_POST['user_id'];
                    $category_id = (int)$_POST['category_id'];
                    $force_issue = isset($_POST['force_issue']) && $_POST['force_issue'] === '1';

                    // Validate inputs
                    if ($user_id <= 0 || $category_id <= 0) {
                        throw new Exception("Invalid user or category selected.");
                    }

                    // Verify user exists and is a student
                    $stmt = $pdo->prepare("SELECT username, email, role FROM users WHERE id = ? AND role = 'student'");
                    $stmt->execute([$user_id]);
                    $user = $stmt->fetch();

                    if (!$user) {
                        throw new Exception("User not found or is not a student.");
                    }

                    // Verify category exists
                    $stmt = $pdo->prepare("SELECT name, icon FROM categories WHERE id = ?");
                    $stmt->execute([$category_id]);
                    $category = $stmt->fetch();

                    if (!$category) {
                        throw new Exception("Category not found.");
                    }

                    // Check if certificate already exists
                    $stmt = $pdo->prepare("SELECT id, certificate_code FROM user_certificates WHERE user_id = ? AND category_id = ?");
                    $stmt->execute([$user_id, $category_id]);
                    $existing_cert = $stmt->fetch();

                    if ($existing_cert && !$force_issue) {
                        $error_message = "Certificate already exists for this user and category. Certificate code: " . $existing_cert['certificate_code'];
                    } else {
                        $pdo->beginTransaction();

                        // If forcing issue and certificate exists, revoke the old one first
                        if ($existing_cert && $force_issue) {
                            $stmt = $pdo->prepare("DELETE FROM user_certificates WHERE id = ?");
                            $stmt->execute([$existing_cert['id']]);
                        }

                        // Generate unique certificate code
                        do {
                            $certificate_code = 'TMO-' . strtoupper(substr(md5(uniqid() . microtime()), 0, 8));
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_certificates WHERE certificate_code = ?");
                            $stmt->execute([$certificate_code]);
                        } while ($stmt->fetchColumn() > 0);

                        // Get challenge completion stats for this category
                        $stmt = $pdo->prepare("
                            SELECT
                                COUNT(*) as total_challenges,
                                COUNT(CASE WHEN uc.completed_at IS NOT NULL THEN 1 END) as completed_challenges
                            FROM challenges c
                            LEFT JOIN user_challenges uc ON c.id = uc.challenge_id AND uc.user_id = ?
                            WHERE c.category_id = ?
                        ");
                        $stmt->execute([$user_id, $category_id]);
                        $completion_stats = $stmt->fetch();

                        // Insert certificate with completion stats
                        $stmt = $pdo->prepare("
                            INSERT INTO user_certificates
                            (user_id, category_id, certificate_code, issued_at, challenges_completed, total_challenges, issued_by_admin)
                            VALUES (?, ?, ?, NOW(), ?, ?, 1)
                        ");
                        $stmt->execute([
                            $user_id,
                            $category_id,
                            $certificate_code,
                            $completion_stats['completed_challenges'],
                            $completion_stats['total_challenges']
                        ]);

                        $certificate_id = $pdo->lastInsertId();

                        // Try to generate certificate file using the certificate generator
                        try {
                            require_once '../utils/certificate_generator.php';
                            $generator = new CertificateGenerator($pdo, $user_id);

                            // Create certificate HTML file
                            $cert_dir = '../certificates';
                            if (!file_exists($cert_dir)) {
                                mkdir($cert_dir, 0755, true);
                            }

                            $safe_category_name = preg_replace('/[^A-Za-z0-9\-_]/', '_', $category['name']);
                            $filename = $certificate_code . '_' . $safe_category_name . '_Certificate.html';
                            $filepath = $cert_dir . '/' . $filename;

                            // Generate certificate HTML content
                            $html_content = generateCertificateHTML($certificate_code, $user, $category, $completion_stats);
                            file_put_contents($filepath, $html_content);

                            // Update certificate record with file path
                            $stmt = $pdo->prepare("UPDATE user_certificates SET certificate_path = ? WHERE id = ?");
                            $stmt->execute([$filepath, $certificate_id]);

                        } catch (Exception $cert_gen_error) {
                            // Log the error but don't fail the certificate issuance
                            error_log("Certificate file generation failed: " . $cert_gen_error->getMessage());
                        }

                        // Log admin action
                        $stmt = $pdo->prepare("
                            INSERT INTO admin_actions_logs
                            (admin_username, action_type, action_description, target_user_id, target_username, ip_address, user_agent, success, created_at)
                            VALUES (?, 'CERTIFICATE_ISSUED', ?, ?, ?, ?, ?, 1, NOW())
                        ");
                        $stmt->execute([
                            $_SESSION['username'],
                            "Manually issued certificate for category: " . $category['name'],
                            $user_id,
                            $user['username'],
                            $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                            $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
                        ]);

                        $pdo->commit();

                        $success_message = "Certificate issued successfully! Certificate code: " . $certificate_code .
                                         " for " . $user['username'] . " in " . $category['name'] . ".";
                    }
                } catch (Exception $e) {
                    if ($pdo->inTransaction()) {
                        $pdo->rollBack();
                    }
                    $error_message = "Error issuing certificate: " . $e->getMessage();

                    // Log the error
                    error_log("Certificate issuance error: " . $e->getMessage() . " - User: " . ($_SESSION['username'] ?? 'Unknown'));
                }
                break;
        }
    }
}

// Pagination and filtering
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 15;
$offset = ($page - 1) * $per_page;

$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build WHERE conditions
$where_conditions = [];
$params = [];

if ($category_filter > 0) {
    $where_conditions[] = "uc.category_id = ?";
    $params[] = $category_filter;
}

if ($search) {
    $where_conditions[] = "(u.username LIKE ? OR uc.certificate_code LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get total certificates count
$count_sql = "
    SELECT COUNT(*)
    FROM user_certificates uc
    JOIN users u ON uc.user_id = u.id
    $where_clause
";
$stmt = $pdo->prepare($count_sql);
$stmt->execute($params);
$total_certificates = $stmt->fetchColumn();
$total_pages = ceil($total_certificates / $per_page);

// Get certificates with user and category info
$sql = "
    SELECT
        uc.*,
        u.username,
        u.email,
        cat.name as category_name,
        cat.icon as category_icon
    FROM user_certificates uc
    JOIN users u ON uc.user_id = u.id
    JOIN categories cat ON uc.category_id = cat.id
    $where_clause
    ORDER BY uc.issued_at DESC
    LIMIT $per_page OFFSET $offset
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$certificates = $stmt->fetchAll();

// Get categories for filter
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll();

// Get certificate statistics
$stmt = $pdo->query("
    SELECT
        COUNT(*) as total_certificates,
        COUNT(DISTINCT user_id) as unique_recipients,
        COUNT(CASE WHEN DATE(issued_at) = CURDATE() THEN 1 END) as issued_today,
        COUNT(CASE WHEN DATE(issued_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as issued_this_week
    FROM user_certificates
");
$cert_stats = $stmt->fetch();

// Get category distribution
$stmt = $pdo->query("
    SELECT
        cat.name as category_name,
        COUNT(*) as certificate_count
    FROM user_certificates uc
    JOIN categories cat ON uc.category_id = cat.id
    GROUP BY cat.id, cat.name
    ORDER BY certificate_count DESC
");
$category_distribution = $stmt->fetchAll();

// Helper function to generate certificate HTML
function generateCertificateHTML($certificate_code, $user, $category, $completion_stats) {
    $issue_date = date('F j, Y');
    $completion_percentage = $completion_stats['total_challenges'] > 0 ?
        round(($completion_stats['completed_challenges'] / $completion_stats['total_challenges']) * 100) : 0;

    return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Completion - ' . htmlspecialchars($category['name']) . '</title>
    <style>
        body {
            font-family: "Georgia", serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .certificate {
            background: white;
            padding: 60px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 800px;
            width: 100%;
            border: 8px solid #f8f9fa;
            position: relative;
        }
        .certificate::before {
            content: "";
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 3px solid #667eea;
            border-radius: 10px;
        }
        .header {
            margin-bottom: 40px;
        }
        .logo {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 20px;
        }
        .title {
            font-size: 36px;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .subtitle {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 40px;
        }
        .recipient {
            font-size: 28px;
            color: #2c3e50;
            margin: 30px 0;
            font-weight: bold;
        }
        .category {
            font-size: 24px;
            color: #667eea;
            margin: 20px 0;
            font-style: italic;
        }
        .completion-info {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .stat {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
        }
        .certificate-code {
            font-family: "Courier New", monospace;
            font-size: 16px;
            color: #2c3e50;
            background: #ecf0f1;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 30px 0;
            display: inline-block;
        }
        .date {
            font-size: 16px;
            color: #7f8c8d;
            margin: 20px 0;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
            align-items: end;
        }
        .signature {
            text-align: center;
            flex: 1;
        }
        .signature-line {
            border-top: 2px solid #2c3e50;
            margin: 20px 20px 10px 20px;
        }
        .signature-title {
            font-size: 14px;
            color: #7f8c8d;
        }
        .verification-url {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 30px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="header">
            <div class="logo">🏆</div>
            <div class="title">Certificate of Completion</div>
            <div class="subtitle">This certifies that</div>
        </div>

        <div class="recipient">' . htmlspecialchars($user['username']) . '</div>

        <div class="subtitle">has successfully completed</div>

        <div class="category">' . htmlspecialchars($category['name']) . '</div>

        <div class="completion-info">
            <div class="stats">
                <div class="stat">
                    <div class="stat-number">' . $completion_stats['completed_challenges'] . '</div>
                    <div class="stat-label">Challenges Completed</div>
                </div>
                <div class="stat">
                    <div class="stat-number">' . $completion_stats['total_challenges'] . '</div>
                    <div class="stat-label">Total Challenges</div>
                </div>
                <div class="stat">
                    <div class="stat-number">' . $completion_percentage . '%</div>
                    <div class="stat-label">Completion Rate</div>
                </div>
            </div>
        </div>

        <div class="certificate-code">Certificate ID: ' . htmlspecialchars($certificate_code) . '</div>

        <div class="date">Issued on ' . $issue_date . '</div>

        <div class="signature-section">
            <div class="signature">
                <div class="signature-line"></div>
                <div class="signature-title">Platform Administrator</div>
            </div>
            <div class="signature">
                <div class="signature-line"></div>
                <div class="signature-title">TryMeOut Platform</div>
            </div>
        </div>

        <div class="verification-url">
            Verify at: ' . $_SERVER['HTTP_HOST'] . '/utils/verify_certificate.php?code=' . htmlspecialchars($certificate_code) . '
        </div>
    </div>
</body>
</html>';
}

include 'includes/admin_header.php';
?>

<style>
    .certificate-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 20px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
        margin-bottom: 16px;
    }

    .certificate-card:hover {
        box-shadow: var(--shadow-md);
        border-color: var(--gray-300);
    }

    .certificate-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .certificate-user {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .user-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 16px;
    }

    .certificate-info {
        flex: 1;
    }

    .certificate-code {
        font-family: 'Courier New', monospace;
        background: var(--gray-100);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        color: var(--gray-700);
    }

    .certificate-category {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;
        background: var(--primary-color);
        color: white;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
    }

    .certificate-actions {
        display: flex;
        gap: 8px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: var(--border-radius-lg);
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        text-align: center;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 4px;
    }

    .stat-label {
        font-size: 12px;
        color: var(--gray-500);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .filters-container {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 20px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        margin-bottom: 24px;
    }

    .certificate-preview {
        width: 60px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        margin-right: 12px;
    }

    .distribution-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid var(--gray-100);
    }

    .distribution-item:last-child {
        border-bottom: none;
    }

    .distribution-bar {
        height: 6px;
        background: var(--gray-200);
        border-radius: 3px;
        overflow: hidden;
        margin-top: 4px;
    }

    .distribution-fill {
        height: 100%;
        background: var(--primary-color);
        transition: width 0.3s ease;
    }
</style>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= $success_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?= $error_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Statistics Overview -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value"><?= number_format($cert_stats['total_certificates']) ?></div>
        <div class="stat-label">Total Certificates</div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?= number_format($cert_stats['unique_recipients']) ?></div>
        <div class="stat-label">Unique Recipients</div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?= number_format($cert_stats['issued_today']) ?></div>
        <div class="stat-label">Issued Today</div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?= number_format($cert_stats['issued_this_week']) ?></div>
        <div class="stat-label">This Week</div>
    </div>
</div>

<div class="row g-4">
    <!-- Certificates List -->
    <div class="col-lg-8">
        <!-- Filters -->
        <div class="filters-container">
            <form method="GET" class="d-flex align-items-center gap-3 flex-wrap">
                <div class="flex-grow-1">
                    <input type="text" name="search" class="form-control" placeholder="Search by username or certificate code..."
                           value="<?= htmlspecialchars($search) ?>">
                </div>

                <select name="category" class="form-select" style="width: auto;">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $category['id'] ?>" <?= $category_filter == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Filter
                </button>

                <a href="certificates.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>Clear
                </a>
            </form>
        </div>

        <!-- Bulk Actions Bar -->
        <div class="bulk-actions-bar" id="bulkActionsBar" style="display: none;">
            <div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded mb-3">
                <div>
                    <span id="selectedCount">0</span> certificates selected
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-warning" onclick="bulkRegenerate()">
                        <i class="fas fa-redo me-1"></i>Regenerate Selected
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="bulkRevoke()">
                        <i class="fas fa-ban me-1"></i>Revoke Selected
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="clearSelection()">
                        <i class="fas fa-times me-1"></i>Clear Selection
                    </button>
                </div>
            </div>
        </div>

        <!-- Certificates List -->
        <form id="bulkForm" method="POST">
            <input type="hidden" name="action" id="bulkAction">
            <div class="certificates-list">
                <?php if (empty($certificates)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-certificate fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No certificates found</h4>
                        <p class="text-muted">Try adjusting your filters or check back later.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($certificates as $certificate): ?>
                        <div class="certificate-card">
                            <div class="certificate-header">
                                <div class="certificate-user">
                                    <div class="form-check me-3">
                                        <input class="form-check-input certificate-checkbox" type="checkbox"
                                               value="<?= $certificate['id'] ?>" name="certificate_ids[]"
                                               onchange="updateBulkActions()">
                                    </div>
                                    <div class="certificate-preview">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                    <div class="user-avatar">
                                        <?= strtoupper(substr($certificate['username'], 0, 2)) ?>
                                    </div>
                                    <div class="certificate-info">
                                        <div class="fw-semibold"><?= htmlspecialchars($certificate['username']) ?></div>
                                        <div class="text-muted small"><?= htmlspecialchars($certificate['email']) ?></div>
                                        <div class="certificate-code mt-1"><?= htmlspecialchars($certificate['certificate_code']) ?></div>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <div class="certificate-category">
                                        <i class="<?= htmlspecialchars($certificate['category_icon']) ?>"></i>
                                        <?= htmlspecialchars($certificate['category_name']) ?>
                                    </div>
                                    <div class="text-muted small mt-1">
                                        Issued: <?= date('M j, Y', strtotime($certificate['issued_at'])) ?>
                                    </div>
                                </div>
                            </div>

                            <div class="certificate-actions">
                                <a href="../utils/verify_certificate.php?code=<?= urlencode($certificate['certificate_code']) ?>"
                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>View
                                </a>

                                <form method="POST" style="display: inline;"
                                      onsubmit="return confirm('Are you sure you want to regenerate this certificate?')">
                                    <input type="hidden" name="action" value="regenerate_certificate">
                                    <input type="hidden" name="certificate_id" value="<?= $certificate['id'] ?>">
                                    <button type="submit" class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-redo me-1"></i>Regenerate
                                    </button>
                                </form>

                                <form method="POST" style="display: inline;"
                                      onsubmit="return confirm('Are you sure you want to revoke this certificate? This action cannot be undone.')">
                                    <input type="hidden" name="action" value="revoke_certificate">
                                    <input type="hidden" name="certificate_id" value="<?= $certificate['id'] ?>">
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-ban me-1"></i>Revoke
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </form>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Certificates pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?><?= $category_filter ? '&category=' . $category_filter : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>

    <!-- Category Distribution -->
    <div class="col-lg-4">
        <div class="certificate-card">
            <h3 class="h5 mb-3">
                <i class="fas fa-chart-pie me-2"></i>
                Category Distribution
            </h3>

            <div class="category-distribution">
                <?php
                $max_count = !empty($category_distribution) ? max(array_column($category_distribution, 'certificate_count')) : 1;
                foreach ($category_distribution as $dist):
                ?>
                    <div class="distribution-item">
                        <div>
                            <div class="fw-semibold"><?= htmlspecialchars($dist['category_name']) ?></div>
                            <div class="distribution-bar">
                                <div class="distribution-fill" style="width: <?= ($dist['certificate_count'] / $max_count) * 100 ?>%"></div>
                            </div>
                        </div>
                        <div class="fw-semibold"><?= $dist['certificate_count'] ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="certificate-card">
            <h3 class="h5 mb-3">
                <i class="fas fa-tools me-2"></i>
                Quick Actions
            </h3>

            <div class="d-grid gap-2">
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>Export Certificates
                    </button>
                    <ul class="dropdown-menu w-100">
                        <li><h6 class="dropdown-header">Export Format</h6></li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="exportCertificates('csv')">
                                <i class="fas fa-file-csv me-2 text-success"></i>
                                Export as CSV
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="exportCertificates('pdf')">
                                <i class="fas fa-file-pdf me-2 text-danger"></i>
                                Export as PDF
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="exportCertificates('simple')">
                                <i class="fas fa-file-csv me-2 text-info"></i>
                                Simple Export (Fallback)
                            </a>
                        </li>
                    </ul>
                </div>

                <button class="btn btn-outline-info" onclick="generateReport()">
                    <i class="fas fa-chart-bar me-2"></i>Generate PDF Report
                </button>

                <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#issueCertificateModal">
                    <i class="fas fa-plus me-2"></i>Issue Certificate
                </button>

                <button class="btn btn-outline-warning" onclick="toggleBulkMode()">
                    <i class="fas fa-tasks me-2"></i>Bulk Actions Mode
                </button>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Issue Certificate Modal -->
<div class="modal fade" id="issueCertificateModal" tabindex="-1" aria-labelledby="issueCertificateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="issueCertificateModalLabel">
                    <i class="fas fa-certificate me-2"></i>Issue New Certificate
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="issueCertificateForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="issue_certificate">
                    <input type="hidden" name="force_issue" id="force_issue" value="0">

                    <div class="mb-3">
                        <label for="user_id" class="form-label">
                            <i class="fas fa-user me-1"></i>Select User
                        </label>
                        <select class="form-select" id="user_id" name="user_id" required onchange="checkExistingCertificate()">
                            <option value="">Choose a user...</option>
                            <?php
                            $stmt = $pdo->query("SELECT id, username, email FROM users WHERE role = 'student' ORDER BY username");
                            $users = $stmt->fetchAll();
                            foreach ($users as $user):
                            ?>
                                <option value="<?= $user['id'] ?>" data-username="<?= htmlspecialchars($user['username']) ?>" data-email="<?= htmlspecialchars($user['email']) ?>">
                                    <?= htmlspecialchars($user['username']) ?> (<?= htmlspecialchars($user['email']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Select the student who will receive the certificate.</div>
                    </div>

                    <div class="mb-3">
                        <label for="category_id" class="form-label">
                            <i class="fas fa-folder me-1"></i>Select Category
                        </label>
                        <select class="form-select" id="category_id" name="category_id" required onchange="checkExistingCertificate()">
                            <option value="">Choose a category...</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>" data-name="<?= htmlspecialchars($category['name']) ?>">
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Select the category for which the certificate will be issued.</div>
                    </div>

                    <!-- User Progress Info -->
                    <div id="userProgressInfo" class="mb-3" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-chart-line me-1"></i>User Progress
                                </h6>
                                <div id="progressDetails">
                                    <!-- Progress details will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Existing Certificate Warning -->
                    <div id="existingCertWarning" class="alert alert-warning" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> A certificate already exists for this user and category.
                        <div class="mt-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="forceIssueCheck" onchange="toggleForceIssue()">
                                <label class="form-check-label" for="forceIssueCheck">
                                    Replace existing certificate (this will revoke the old certificate)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> A certificate will be automatically generated with a unique code.
                        The user will be able to download and verify their certificate. An HTML certificate file will also be created for download.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="issueCertBtn">
                        <span class="btn-content">
                            <i class="fas fa-certificate me-2"></i>Issue Certificate
                        </span>
                        <span class="btn-loading d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            Issuing...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function exportCertificates(format = 'csv') {
    showNotification('Preparing export...', 'info');

    let exportUrl;
    let filename;

    if (format === 'simple') {
        exportUrl = 'simple_export.php';
        filename = 'certificates_simple_export_' + new Date().toISOString().split('T')[0] + '.csv';
        showNotification('Using simple export method...', 'info');
    } else if (format === 'pdf') {
        exportUrl = 'export_certificates_pdf.php?download=true';
        filename = 'certificates_export_' + new Date().toISOString().split('T')[0] + '.pdf';
        showNotification('Generating PDF export...', 'info');

        // For PDF, open in new tab for print dialog
        const newWindow = window.open(exportUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        if (newWindow) {
            setTimeout(() => {
                showNotification('PDF export opened in new tab. The print dialog will appear automatically to save as PDF.', 'success');
            }, 1000);
        } else {
            showNotification('Please allow popups to export PDF. Check your browser\'s popup blocker.', 'warning');
        }
        return; // Exit early for PDF
    } else {
        exportUrl = 'export_certificates.php';
        filename = 'certificates_export_' + new Date().toISOString().split('T')[0] + '.csv';
    }

    try {
        console.log('Attempting to export from:', exportUrl);

        // Method 1: Create invisible link and click it
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = filename;
        link.style.display = 'none';
        link.target = '_blank';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
            showNotification('Export started! Check your downloads folder.', 'success');
        }, 1000);

    } catch (error) {
        console.error('Export error:', error);
        showNotification('Export failed. Trying alternative method...', 'warning');

        // Fallback: Direct window.open
        try {
            window.open(exportUrl, '_blank');
            setTimeout(() => {
                showNotification('Export opened in new tab. Save the file manually if needed.', 'info');
            }, 1000);
        } catch (fallbackError) {
            console.error('Fallback export error:', fallbackError);
            showNotification('Export failed. Please contact administrator.', 'danger');
        }
    }
}

// Alternative export function for testing
function testExport() {
    showNotification('Testing export functionality...', 'info');
    window.open('test_export.php', '_blank');
}

function generateReport() {
    showNotification('Generating PDF report...', 'info');

    const newWindow = window.open('generate_certificate_report.php?download=true', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
    if (newWindow) {
        setTimeout(() => {
            showNotification('PDF report opened in new tab. The print dialog will appear automatically.', 'success');
        }, 1000);
    } else {
        showNotification('Please allow popups to generate PDF report.', 'warning');
    }
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.certificate-checkbox:checked');
    const bulkBar = document.getElementById('bulkActionsBar');
    const selectedCount = document.getElementById('selectedCount');

    if (checkboxes.length > 0) {
        bulkBar.style.display = 'block';
        selectedCount.textContent = checkboxes.length;
    } else {
        bulkBar.style.display = 'none';
    }
}

function bulkRegenerate() {
    const checkboxes = document.querySelectorAll('.certificate-checkbox:checked');
    if (checkboxes.length === 0) {
        showNotification('Please select certificates to regenerate.', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to regenerate ${checkboxes.length} certificate(s)? This will generate new certificate codes.`)) {
        document.getElementById('bulkAction').value = 'bulk_regenerate';
        document.getElementById('bulkForm').submit();
    }
}

function bulkRevoke() {
    const checkboxes = document.querySelectorAll('.certificate-checkbox:checked');
    if (checkboxes.length === 0) {
        showNotification('Please select certificates to revoke.', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to revoke ${checkboxes.length} certificate(s)? This action cannot be undone.`)) {
        document.getElementById('bulkAction').value = 'bulk_revoke';
        document.getElementById('bulkForm').submit();
    }
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.certificate-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = false);
    updateBulkActions();
}

function toggleBulkMode() {
    const checkboxes = document.querySelectorAll('.certificate-checkbox');
    const bulkBar = document.getElementById('bulkActionsBar');

    if (bulkBar.style.display === 'none' || !bulkBar.style.display) {
        // Show bulk mode
        bulkBar.style.display = 'block';
        showNotification('Bulk actions mode enabled. Select certificates to perform bulk operations.', 'info');
    } else {
        // Hide bulk mode and clear selections
        clearSelection();
        showNotification('Bulk actions mode disabled.', 'info');
    }
}

// Select all functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add select all checkbox to bulk actions bar
    const bulkBar = document.getElementById('bulkActionsBar');
    if (bulkBar) {
        const selectAllHtml = `
            <div class="form-check me-3">
                <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                <label class="form-check-label" for="selectAll">
                    Select All
                </label>
            </div>
        `;
        const selectedCountElement = bulkBar.querySelector('div:first-child');
        selectedCountElement.insertAdjacentHTML('afterbegin', selectAllHtml);
    }
});

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.certificate-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBulkActions();
}

// Enhanced certificate issuance functions
function checkExistingCertificate() {
    const userId = document.getElementById('user_id').value;
    const categoryId = document.getElementById('category_id').value;

    if (!userId || !categoryId) {
        document.getElementById('existingCertWarning').style.display = 'none';
        document.getElementById('userProgressInfo').style.display = 'none';
        return;
    }

    // Check for existing certificate
    fetch('check_certificate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `user_id=${userId}&category_id=${categoryId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.exists) {
            document.getElementById('existingCertWarning').style.display = 'block';
            document.getElementById('issueCertBtn').disabled = true;
        } else {
            document.getElementById('existingCertWarning').style.display = 'none';
            document.getElementById('issueCertBtn').disabled = false;
            document.getElementById('force_issue').value = '0';
        }

        // Show user progress info
        if (data.progress) {
            const progressDiv = document.getElementById('progressDetails');
            const completionRate = data.progress.total > 0 ?
                Math.round((data.progress.completed / data.progress.total) * 100) : 0;

            progressDiv.innerHTML = `
                <div class="row text-center">
                    <div class="col-4">
                        <div class="fw-bold text-primary">${data.progress.completed}</div>
                        <small class="text-muted">Completed</small>
                    </div>
                    <div class="col-4">
                        <div class="fw-bold text-info">${data.progress.total}</div>
                        <small class="text-muted">Total</small>
                    </div>
                    <div class="col-4">
                        <div class="fw-bold text-success">${completionRate}%</div>
                        <small class="text-muted">Progress</small>
                    </div>
                </div>
                <div class="progress mt-2">
                    <div class="progress-bar" role="progressbar" style="width: ${completionRate}%"
                         aria-valuenow="${completionRate}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            `;
            document.getElementById('userProgressInfo').style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error checking certificate:', error);
    });
}

function toggleForceIssue() {
    const forceCheck = document.getElementById('forceIssueCheck');
    const forceInput = document.getElementById('force_issue');
    const issueBtn = document.getElementById('issueCertBtn');

    if (forceCheck.checked) {
        forceInput.value = '1';
        issueBtn.disabled = false;
        issueBtn.classList.remove('btn-primary');
        issueBtn.classList.add('btn-warning');
        issueBtn.querySelector('.btn-content').innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Replace Certificate';
    } else {
        forceInput.value = '0';
        issueBtn.disabled = true;
        issueBtn.classList.remove('btn-warning');
        issueBtn.classList.add('btn-primary');
        issueBtn.querySelector('.btn-content').innerHTML = '<i class="fas fa-certificate me-2"></i>Issue Certificate';
    }
}

// Enhanced form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('issueCertificateForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const btn = document.getElementById('issueCertBtn');
            const btnContent = btn.querySelector('.btn-content');
            const btnLoading = btn.querySelector('.btn-loading');

            // Show loading state
            btnContent.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            btn.disabled = true;

            // The form will submit normally, but we show loading state
        });
    }
});
</script>

<?php include 'includes/admin_footer.php'; ?>
