<?php
session_start();
require '../config/db_connect.php';
require '../config/brute_force_protection.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Audit Trail Test</h1>";
echo "<p>This script tests the new audit trail functionality for brute force protection.</p>";

$bruteForceProtection = new BruteForceProtection($pdo);

// Handle test actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_test_lockout'])) {
        $test_email = 'audit-test-' . time() . '@example.com';
        
        echo "<h2>Creating Test Lockout</h2>";
        
        // Create failed attempts to trigger lockout
        for ($i = 0; $i < 5; $i++) {
            $result = $bruteForceProtection->recordFailedAttempt($test_email, '127.0.0.1', 'Test User Agent');
        }
        
        if ($result['locked']) {
            echo "✅ Test lockout created for: " . htmlspecialchars($test_email) . "<br>";
            echo "Attempt count: " . $result['attempt_count'] . "<br>";
        } else {
            echo "❌ Failed to create test lockout<br>";
        }
    }
    
    if (isset($_POST['test_manual_unlock'])) {
        $email = $_POST['test_email'];
        $admin_username = $_SESSION['username'] ?? 'Test Admin';
        $reason = $_POST['test_reason'] ?? 'Testing audit trail functionality';
        
        echo "<h2>Testing Manual Unlock</h2>";
        
        $result = $bruteForceProtection->manualUnlock($email, $admin_username, $reason);
        
        if ($result['success']) {
            echo "✅ Manual unlock successful<br>";
            echo "Email: " . htmlspecialchars($email) . "<br>";
            echo "Unlocked by: " . htmlspecialchars($result['unlocked_by']) . "<br>";
            echo "Unlocked at: " . $result['unlocked_at'] . "<br>";
        } else {
            echo "❌ Manual unlock failed: " . $result['message'] . "<br>";
        }
    }
}

echo "<h2>Current Lockout Status</h2>";

// Show current lockouts
try {
    $stmt = $pdo->query("
        SELECT email, locked_at, unlock_at, status, manually_unlocked, 
               unlocked_by, unlocked_at, unlock_reason, attempt_count
        FROM account_lockouts 
        WHERE locked_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY locked_at DESC
    ");
    $lockouts = $stmt->fetchAll();
    
    if (empty($lockouts)) {
        echo "ℹ️ No recent lockouts found<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Email</th><th>Locked At</th><th>Status</th><th>Manually Unlocked</th>";
        echo "<th>Unlocked By</th><th>Unlocked At</th><th>Reason</th><th>Attempts</th>";
        echo "</tr>";
        
        foreach ($lockouts as $lockout) {
            $rowClass = '';
            if ($lockout['status'] === 'active') $rowClass = 'background: #fff3cd;';
            if ($lockout['status'] === 'manually_unlocked') $rowClass = 'background: #d1ecf1;';
            if ($lockout['status'] === 'expired') $rowClass = 'background: #f8f9fa;';
            
            echo "<tr style='$rowClass'>";
            echo "<td>" . htmlspecialchars($lockout['email']) . "</td>";
            echo "<td>" . $lockout['locked_at'] . "</td>";
            echo "<td><strong>" . ucfirst($lockout['status']) . "</strong></td>";
            echo "<td>" . ($lockout['manually_unlocked'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . htmlspecialchars($lockout['unlocked_by'] ?? '-') . "</td>";
            echo "<td>" . ($lockout['unlocked_at'] ?? '-') . "</td>";
            echo "<td>" . htmlspecialchars($lockout['unlock_reason'] ?? '-') . "</td>";
            echo "<td>" . $lockout['attempt_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ Error fetching lockouts: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Actions</h2>";

// Create test lockout form
echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>1. Create Test Lockout</h3>";
echo "<form method='POST'>";
echo "<button type='submit' name='create_test_lockout' class='btn btn-warning'>Create Test Lockout</button>";
echo "</form>";
echo "<p><small>This will create a new test account and trigger a lockout by making 5 failed attempts.</small></p>";
echo "</div>";

// Manual unlock test form
echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>2. Test Manual Unlock</h3>";
echo "<form method='POST'>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label>Email to unlock:</label><br>";
echo "<select name='test_email' required style='width: 300px; padding: 5px;'>";
echo "<option value=''>Select an email...</option>";

// Get active lockouts for testing
$stmt = $pdo->query("
    SELECT DISTINCT email 
    FROM account_lockouts 
    WHERE status = 'active' AND unlock_at > NOW()
    ORDER BY locked_at DESC
");
$active_lockouts = $stmt->fetchAll();

foreach ($active_lockouts as $lockout) {
    echo "<option value='" . htmlspecialchars($lockout['email']) . "'>" . htmlspecialchars($lockout['email']) . "</option>";
}
echo "</select>";
echo "</div>";

echo "<div style='margin-bottom: 10px;'>";
echo "<label>Unlock reason:</label><br>";
echo "<select name='test_reason' style='width: 300px; padding: 5px;'>";
echo "<option value='Testing audit trail functionality'>Testing audit trail functionality</option>";
echo "<option value='False positive - legitimate user'>False positive - legitimate user</option>";
echo "<option value='Administrative override'>Administrative override</option>";
echo "</select>";
echo "</div>";

echo "<button type='submit' name='test_manual_unlock' class='btn btn-info'>Test Manual Unlock</button>";
echo "</form>";
echo "<p><small>This will test the manual unlock functionality with audit trail preservation.</small></p>";
echo "</div>";

echo "<h2>Audit Trail Verification</h2>";

// Show audit trail data
try {
    $stmt = $pdo->query("
        SELECT event_type, username, details, created_at
        FROM security_logs 
        WHERE event_type IN ('ACCOUNT_LOCKED', 'MANUAL_UNLOCK')
        AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $audit_logs = $stmt->fetchAll();
    
    if (empty($audit_logs)) {
        echo "ℹ️ No recent audit logs found<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Event Type</th><th>Username</th><th>Details</th><th>Timestamp</th>";
        echo "</tr>";
        
        foreach ($audit_logs as $log) {
            echo "<tr>";
            echo "<td><strong>" . $log['event_type'] . "</strong></td>";
            echo "<td>" . htmlspecialchars($log['username'] ?? 'System') . "</td>";
            echo "<td><small>" . htmlspecialchars($log['details']) . "</small></td>";
            echo "<td>" . $log['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "ℹ️ Security logs table not available or error: " . $e->getMessage() . "<br>";
}

echo "<h2>Summary</h2>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Audit Trail Features:</strong><br>";
echo "• ✅ Lockout records are preserved instead of deleted<br>";
echo "• ✅ Manual unlocks are tracked with admin username<br>";
echo "• ✅ Unlock reasons are stored for compliance<br>";
echo "• ✅ Status tracking (active, expired, manually_unlocked)<br>";
echo "• ✅ Extended data retention (30 days)<br>";
echo "• ✅ Security event logging for audit purposes<br>";
echo "</div>";

echo "<br><a href='brute_force_management.php'>← Back to Brute Force Management</a>";
echo " | <a href='migrate_brute_force_audit.php'>Run Migration</a>";
?>
