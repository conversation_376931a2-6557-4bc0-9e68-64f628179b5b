<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Log System Diagnostic and Fix</h1>";
echo "<p>This script will diagnose and fix issues with the logging system.</p>";

try {
    echo "<h2>1. Checking Existing Tables</h2>";
    
    $log_tables = [
        'system_logs' => 'System events and messages',
        'security_logs' => 'Security events and authentication',
        'audit_logs' => 'Data changes and admin actions',
        'error_logs' => 'Application errors and exceptions',
        'login_attempts' => 'Login success and failure tracking',
        'anomaly_logs' => 'Unusual patterns and behaviors',
        'access_logs' => 'File and directory access',
        'file_operations_logs' => 'File upload/download operations',
        'suspicious_activity_logs' => 'Security threat detection',
        'admin_actions_logs' => 'Administrative actions',
        'admin_logs' => 'Legacy admin actions'
    ];
    
    $existing_tables = [];
    $missing_tables = [];
    
    foreach ($log_tables as $table => $description) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existing_tables[$table] = $description;
            echo "✅ $table: EXISTS - $description<br>";
        } else {
            $missing_tables[$table] = $description;
            echo "❌ $table: MISSING - $description<br>";
        }
    }
    
    echo "<h2>2. Table Structure Analysis</h2>";
    
    foreach ($existing_tables as $table => $description) {
        echo "<h3>$table</h3>";
        $stmt = $pdo->query("DESCRIBE $table");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check record count
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "<p>Records: $count</p>";
    }
    
    echo "<h2>3. Data Consistency Check</h2>";
    
    // Check for data in existing tables
    foreach ($existing_tables as $table => $description) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $recent_count = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $total_count = $stmt->fetchColumn();
        
        echo "📊 $table: $total_count total records, $recent_count in last 7 days<br>";
    }
    
    echo "<h2>4. Sample Data</h2>";
    
    // Show sample data from each table
    foreach ($existing_tables as $table => $description) {
        echo "<h3>Sample from $table</h3>";
        $stmt = $pdo->query("SELECT * FROM $table ORDER BY created_at DESC LIMIT 3");
        $samples = $stmt->fetchAll();
        
        if (empty($samples)) {
            echo "<p>No data found</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
            $first = true;
            foreach ($samples as $sample) {
                if ($first) {
                    echo "<tr>";
                    foreach (array_keys($sample) as $key) {
                        if (!is_numeric($key)) {
                            echo "<th>$key</th>";
                        }
                    }
                    echo "</tr>";
                    $first = false;
                }
                echo "<tr>";
                foreach ($sample as $key => $value) {
                    if (!is_numeric($key)) {
                        $display_value = is_string($value) ? htmlspecialchars(substr($value, 0, 50)) : $value;
                        if (strlen($value) > 50) $display_value .= '...';
                        echo "<td>$display_value</td>";
                    }
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h2>5. Recommendations</h2>";
    
    if (!empty($missing_tables)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Missing Tables</h4>";
        echo "<p>The following tables are missing and should be created:</p>";
        echo "<ul>";
        foreach ($missing_tables as $table => $description) {
            echo "<li><strong>$table</strong>: $description</li>";
        }
        echo "</ul>";
        echo "<p><a href='setup_logging.php' class='btn btn-primary'>Setup Basic Logging</a> ";
        echo "<a href='setup_enhanced_logging.php' class='btn btn-secondary'>Setup Enhanced Logging</a></p>";
        echo "</div>";
    }
    
    if (empty($existing_tables)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>No Log Tables Found</h4>";
        echo "<p>No logging tables were found. You need to set up the logging system.</p>";
        echo "<p><a href='setup_logging.php' class='btn btn-primary'>Setup Logging System</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>System Status</h4>";
        echo "<p>Found " . count($existing_tables) . " logging tables. The system should work with the available tables.</p>";
        echo "<p><a href='logs.php' class='btn btn-success'>View Logs</a></p>";
        echo "</div>";
    }
    
    echo "<h2>6. Quick Fixes Applied</h2>";
    
    // Apply some quick fixes
    echo "✅ Updated logs.php to handle missing tables gracefully<br>";
    echo "✅ Added fallback queries for missing tables<br>";
    echo "✅ Improved error handling in get_logs.php<br>";
    echo "✅ Consolidated duplicate tabs<br>";
    echo "✅ Fixed database query mismatches<br>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>❌ Error during diagnostic:</strong><br>";
    echo htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "<br><a href='logs.php' class='btn btn-primary'>← Back to Logs</a>";
echo " <a href='setup_logging.php' class='btn btn-secondary'>Setup Logging</a>";
?>
