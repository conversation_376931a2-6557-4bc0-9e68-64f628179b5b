<?php
session_start();
require '../config/db_connect.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Badge System Diagnostic</h1>";

try {
    echo "<h2>1. Checking Table Structure</h2>";
    
    // Check if required tables exist
    $tables = ['users', 'categories', 'challenges', 'user_progress', 'badges', 'user_badges'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
            
            // Show table structure
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll();
            echo "<small>Columns: " . implode(', ', array_column($columns, 'Field')) . "</small><br>";
        } else {
            echo "❌ Table '$table' missing<br>";
        }
    }
    
    echo "<h2>2. Sample Data Check</h2>";
    
    // Check users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'student'");
    $student_count = $stmt->fetchColumn();
    echo "Students: $student_count<br>";
    
    // Check categories
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
    $category_count = $stmt->fetchColumn();
    echo "Categories: $category_count<br>";
    
    // Check challenges
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM challenges");
    $challenge_count = $stmt->fetchColumn();
    echo "Challenges: $challenge_count<br>";
    
    // Check user progress
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_progress WHERE status = 'completed'");
    $completed_count = $stmt->fetchColumn();
    echo "Completed challenges: $completed_count<br>";
    
    // Check existing badges
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM badges");
    $badge_count = $stmt->fetchColumn();
    echo "Badges defined: $badge_count<br>";
    
    // Check user badges
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_badges");
    $user_badge_count = $stmt->fetchColumn();
    echo "User badges awarded: $user_badge_count<br>";
    
    echo "<h2>3. Testing Category Completion Query</h2>";
    
    // Simple test query
    $stmt = $pdo->query("
        SELECT 
            u.username,
            cat.name as category_name,
            COUNT(DISTINCT c.id) as total_challenges,
            COUNT(DISTINCT CASE WHEN up.status = 'completed' THEN up.challenge_id END) as completed_challenges
        FROM users u
        CROSS JOIN categories cat
        LEFT JOIN challenges c ON cat.id = c.category_id
        LEFT JOIN user_progress up ON u.id = up.user_id AND c.id = up.challenge_id
        WHERE u.role = 'student'
        GROUP BY u.id, cat.id, u.username, cat.name
        HAVING total_challenges > 0 
        AND completed_challenges = total_challenges 
        AND completed_challenges > 0
        ORDER BY u.username, cat.name
        LIMIT 10
    ");
    $completions = $stmt->fetchAll();
    
    echo "Found " . count($completions) . " category completions:<br>";
    foreach ($completions as $completion) {
        echo "• {$completion['username']} completed {$completion['category_name']} ({$completion['completed_challenges']}/{$completion['total_challenges']})<br>";
    }
    
    echo "<h2>4. Testing Badge Award Query</h2>";
    
    // Check for missing badges
    $stmt = $pdo->query("
        SELECT 
            u.username,
            cat.name as category_name,
            COUNT(DISTINCT c.id) as total_challenges,
            COUNT(DISTINCT CASE WHEN up.status = 'completed' THEN up.challenge_id END) as completed_challenges,
            COUNT(ub.id) as existing_badges
        FROM users u
        CROSS JOIN categories cat
        LEFT JOIN challenges c ON cat.id = c.category_id
        LEFT JOIN user_progress up ON u.id = up.user_id AND c.id = up.challenge_id
        LEFT JOIN user_badges ub ON u.id = ub.user_id 
        LEFT JOIN badges b ON ub.badge_id = b.id AND b.category_id = cat.id
        WHERE u.role = 'student'
        GROUP BY u.id, cat.id, u.username, cat.name
        HAVING total_challenges > 0 
        AND completed_challenges = total_challenges 
        AND completed_challenges > 0
        AND existing_badges = 0
        ORDER BY u.username, cat.name
        LIMIT 5
    ");
    $missing = $stmt->fetchAll();
    
    echo "Found " . count($missing) . " missing badges:<br>";
    foreach ($missing as $miss) {
        echo "• {$miss['username']} should have badge for {$miss['category_name']} ({$miss['completed_challenges']}/{$miss['total_challenges']} completed)<br>";
    }
    
    echo "<h2>5. Testing Badge Creation</h2>";
    
    if (!empty($missing)) {
        $test_missing = $missing[0];
        echo "Testing badge creation for: {$test_missing['username']} - {$test_missing['category_name']}<br>";
        
        // Get category ID
        $stmt = $pdo->prepare("SELECT id FROM categories WHERE name = ?");
        $stmt->execute([$test_missing['category_name']]);
        $category = $stmt->fetch();
        
        if ($category) {
            echo "Category ID: {$category['id']}<br>";
            
            // Check if badge exists
            $stmt = $pdo->prepare("SELECT id FROM badges WHERE category_id = ?");
            $stmt->execute([$category['id']]);
            $badge = $stmt->fetch();
            
            if ($badge) {
                echo "Badge exists with ID: {$badge['id']}<br>";
            } else {
                echo "No badge exists for this category - would need to create one<br>";
            }
        }
    }
    
    echo "<h2>6. Admin Actions Log Test</h2>";
    
    // Test admin actions log
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_actions_logs'");
    if ($stmt->rowCount() > 0) {
        echo "✅ admin_actions_logs table exists<br>";
        
        $stmt = $pdo->query("DESCRIBE admin_actions_logs");
        $columns = $stmt->fetchAll();
        echo "Columns: " . implode(', ', array_column($columns, 'Field')) . "<br>";
        
        // Check enum values for action_type
        foreach ($columns as $column) {
            if ($column['Field'] === 'action_type') {
                echo "Action type enum: {$column['Type']}<br>";
            }
        }
    } else {
        echo "❌ admin_actions_logs table missing<br>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>";
    echo "<h3>Error:</h3>";
    echo "Message: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "</div>";
}

echo "<br><a href='badges.php'>← Back to Badges</a>";
?>
