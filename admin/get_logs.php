<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

$type = $_GET['type'] ?? 'system';
$limit = (int)($_GET['limit'] ?? 100);
$date_range = $_GET['date_range'] ?? 'month';
$log_level = $_GET['log_level'] ?? '';
$user_filter = $_GET['user_filter'] ?? '';
$ip_filter = $_GET['ip_filter'] ?? '';
$start_date = $_GET['start_date'] ?? '';
$end_date = $_GET['end_date'] ?? '';

// Build date condition
$date_condition = '';
switch ($date_range) {
    case 'today':
        $date_condition = "AND DATE(created_at) = CURDATE()";
        break;
    case 'week':
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        break;
    case 'month':
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        break;
    case 'all':
        $date_condition = ""; // No date restriction
        break;
    case 'custom':
        if (!empty($start_date) && !empty($end_date)) {
            $date_condition = "AND DATE(created_at) BETWEEN " . $pdo->quote($start_date) . " AND " . $pdo->quote($end_date);
        } elseif (!empty($start_date)) {
            $date_condition = "AND DATE(created_at) >= " . $pdo->quote($start_date);
        } elseif (!empty($end_date)) {
            $date_condition = "AND DATE(created_at) <= " . $pdo->quote($end_date);
        }
        break;
    default:
        // For audit logs, use a more generous default
        if ($type === 'audit') {
            $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)"; // 3 months for audit logs
        } else {
            $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        }
        break;
}

// Build additional conditions
$additional_conditions = '';

// Log level filter (works for system, security, and other logs with level/risk fields)
if (!empty($log_level)) {
    switch ($type) {
        case 'system':
            $additional_conditions .= " AND log_level = " . $pdo->quote($log_level);
            break;
        case 'security':
        case 'access':
        case 'file_operations':
        case 'suspicious':
            $additional_conditions .= " AND risk_level = " . $pdo->quote($log_level);
            break;
        case 'anomaly':
            $additional_conditions .= " AND severity = " . $pdo->quote($log_level);
            break;
    }
}

// User filter
if (!empty($user_filter)) {
    switch ($type) {
        case 'admin_actions':
            $additional_conditions .= " AND (admin_username LIKE " . $pdo->quote('%' . $user_filter . '%') .
                                     " OR target_username LIKE " . $pdo->quote('%' . $user_filter . '%') . ")";
            break;
        case 'login':
            $additional_conditions .= " AND email LIKE " . $pdo->quote('%' . $user_filter . '%');
            break;
        default:
            $additional_conditions .= " AND username LIKE " . $pdo->quote('%' . $user_filter . '%');
            break;
    }
}

// IP address filter
if (!empty($ip_filter)) {
    $additional_conditions .= " AND ip_address LIKE " . $pdo->quote('%' . $ip_filter . '%');
}

try {
    switch ($type) {
        case 'system':
            $query = "
                SELECT id, log_level, category, message, details, ip_address, created_at
                FROM system_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC 
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderSystemLogs($logs);
            break;

        case 'security':
            $query = "
                SELECT id, event_type, username, ip_address, details, risk_level, created_at
                FROM security_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC 
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderSecurityLogs($logs);
            break;

        case 'audit':
            $query = "
                SELECT id, action_type, table_name, record_id, username, old_values, new_values, ip_address, created_at
                FROM audit_logs
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderAuditLogs($logs);
            break;

        case 'application':
            $query = "
                SELECT id, module, action, user_id, request_method, request_uri, response_code, execution_time, ip_address, created_at
                FROM application_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC 
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderApplicationLogs($logs);
            break;

        case 'error':
            $query = "
                SELECT id, error_type, error_message, file_path, line_number, user_id, ip_address, created_at
                FROM error_logs
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderErrorLogs($logs);
            break;

        case 'login':
            $query = "
                SELECT id, email, user_id, ip_address, country, city, user_agent, success, failure_reason, created_at
                FROM login_attempts
                WHERE 1=1 $date_condition
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderLoginLogs($logs);
            break;

        case 'anomaly':
            $query = "
                SELECT id, anomaly_type, user_id, ip_address, severity, description, data, resolved, created_at
                FROM anomaly_logs
                WHERE 1=1 $date_condition
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderAnomalyLogs($logs);
            break;

        case 'access':
            $query = "
                SELECT id, username, ip_address, request_method, request_uri, file_path, directory_path,
                       access_type, response_code, blocked, block_reason, risk_level, created_at
                FROM access_logs
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderAccessLogs($logs);
            break;

        case 'file_operations':
            $query = "
                SELECT id, username, ip_address, operation_type, file_path, file_name,
                       file_size, file_type, success, error_message, risk_level, created_at
                FROM file_operations_logs
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderFileOperationsLogs($logs);
            break;

        case 'suspicious':
            $query = "
                SELECT id, username, ip_address, activity_type, severity, description,
                       auto_blocked, resolved, resolved_by, created_at
                FROM suspicious_activity_logs
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderSuspiciousLogs($logs);
            break;

        case 'admin_actions':
            $query = "
                SELECT id, admin_username, action_type, action_description, target_username,
                       ip_address, success, error_message, created_at
                FROM admin_actions_logs
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderAdminActionsLogs($logs);
            break;

        case 'performance':
            $query = "
                SELECT id, metric_type, metric_name, value, unit, threshold_exceeded, created_at
                FROM performance_logs
                WHERE 1=1 $date_condition
                ORDER BY created_at DESC
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderPerformanceLogs($logs);
            break;

        default:
            echo '<div class="alert alert-danger">Invalid log type</div>';
    }
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error loading logs: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

function renderSystemLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No system logs found</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Level</th>';
    echo '<th>Category</th>';
    echo '<th>Message</th>';
    echo '<th>IP Address</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        $level_class = 'log-level-' . strtolower($log['log_level']);
        echo '<tr>';
        echo '<td><span class="log-level-badge ' . $level_class . '">' . htmlspecialchars($log['log_level']) . '</span></td>';
        echo '<td>' . htmlspecialchars($log['category']) . '</td>';
        echo '<td>' . htmlspecialchars(substr($log['message'], 0, 100)) . (strlen($log['message']) > 100 ? '...' : '') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        if (!empty($log['details'])) {
            echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'system\')">Details</button>';
        }
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}

function renderSecurityLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No security logs found</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Event Type</th>';
    echo '<th>Username</th>';
    echo '<th>IP Address</th>';
    echo '<th>Risk Level</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        $risk_class = 'risk-level-' . strtolower($log['risk_level']);
        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['event_type']) . '</td>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'Unknown') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td><span class="log-level-badge ' . $risk_class . '">' . htmlspecialchars($log['risk_level']) . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        if (!empty($log['details'])) {
            echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'security\')">Details</button>';
        }
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}

function renderAuditLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4">';
        echo '<div class="alert alert-info">';
        echo '<h5><i class="fas fa-info-circle me-2"></i>No Audit Logs Found</h5>';
        echo '<p class="mb-2">No audit logs found for the selected date range.</p>';
        echo '<p class="mb-0"><strong>Try:</strong></p>';
        echo '<ul class="list-unstyled mb-0">';
        echo '<li>• Expanding the date range to "Last 30 days" or "All Time"</li>';
        echo '<li>• Making changes in <a href="../pages/settings.php" target="_blank">User Settings</a> to generate audit logs</li>';
        echo '<li>• Checking if audit logging is properly configured</li>';
        echo '</ul>';
        echo '</div>';
        echo '</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Action</th>';
    echo '<th>Table</th>';
    echo '<th>Record ID</th>';
    echo '<th>Username</th>';
    echo '<th>IP Address</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        echo '<tr>';
        echo '<td><span class="badge bg-primary">' . htmlspecialchars($log['action_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars($log['table_name']) . '</td>';
        echo '<td>' . htmlspecialchars($log['record_id'] ?? 'N/A') . '</td>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'System') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        if (!empty($log['old_values']) || !empty($log['new_values'])) {
            echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'audit\')">Changes</button>';
        }
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}

function renderApplicationLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No application logs found</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Module</th>';
    echo '<th>Action</th>';
    echo '<th>Method</th>';
    echo '<th>Response</th>';
    echo '<th>Execution Time</th>';
    echo '<th>IP Address</th>';
    echo '<th>Time</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        $response_class = $log['response_code'] >= 400 ? 'text-danger' : ($log['response_code'] >= 300 ? 'text-warning' : 'text-success');
        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['module']) . '</td>';
        echo '<td>' . htmlspecialchars($log['action']) . '</td>';
        echo '<td><span class="badge bg-secondary">' . htmlspecialchars($log['request_method']) . '</span></td>';
        echo '<td><span class="' . $response_class . '">' . htmlspecialchars($log['response_code']) . '</span></td>';
        echo '<td>' . number_format($log['execution_time'], 3) . 's</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}

function renderErrorLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No error logs found</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Error Type</th>';
    echo '<th>Message</th>';
    echo '<th>File</th>';
    echo '<th>Line</th>';
    echo '<th>IP Address</th>';
    echo '<th>Time</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        echo '<tr>';
        echo '<td><span class="badge bg-danger">' . htmlspecialchars($log['error_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars(substr($log['error_message'], 0, 80)) . (strlen($log['error_message']) > 80 ? '...' : '') . '</td>';
        echo '<td>' . htmlspecialchars(basename($log['file_path'] ?? '')) . '</td>';
        echo '<td>' . htmlspecialchars($log['line_number'] ?? '') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}

function renderLoginLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No login logs found</div>';
        return;
    }

    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Status</th>';
    echo '<th>Email</th>';
    echo '<th>User ID</th>';
    echo '<th>IP Address</th>';
    echo '<th>Location</th>';
    echo '<th>User Agent</th>';
    echo '<th>Reason</th>';
    echo '<th>Time</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $status = $log['success'] ? 'SUCCESS' : 'FAILED';
        $badge_class = $log['success'] ? 'success' : 'danger';
        echo '<tr>';
        echo '<td><span class="badge bg-' . $badge_class . '">' . $status . '</span></td>';
        echo '<td>' . htmlspecialchars($log['email']) . '</td>';
        echo '<td>' . ($log['user_id'] ? htmlspecialchars($log['user_id']) : '<span class="text-muted">N/A</span>') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . htmlspecialchars($log['country'] ?? 'Unknown') . ', ' . htmlspecialchars($log['city'] ?? 'Unknown') . '</td>';
        echo '<td><small>' . htmlspecialchars(substr($log['user_agent'] ?? '', 0, 30)) . '...</small></td>';
        echo '<td>' . htmlspecialchars($log['failure_reason'] ?? 'N/A') . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderAnomalyLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No anomaly logs found</div>';
        return;
    }

    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Type</th>';
    echo '<th>Severity</th>';
    echo '<th>Description</th>';
    echo '<th>IP Address</th>';
    echo '<th>Status</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $severity_class = 'log-level-' . strtolower($log['severity']);
        $status = $log['resolved'] ? 'RESOLVED' : 'OPEN';
        $status_class = $log['resolved'] ? 'success' : 'warning';
        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['anomaly_type']) . '</td>';
        echo '<td><span class="log-level-badge ' . $severity_class . '">' . htmlspecialchars($log['severity']) . '</span></td>';
        echo '<td>' . htmlspecialchars(substr($log['description'], 0, 50)) . '...</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td><span class="badge bg-' . $status_class . '">' . $status . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        if (!empty($log['data'])) {
            echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'anomaly\')">Details</button>';
        }
        if (!$log['resolved']) {
            echo ' <button class="btn btn-sm btn-outline-success" onclick="resolveAnomaly(' . $log['id'] . ')">Resolve</button>';
        }
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderAccessLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No access logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>User</th>';
    echo '<th>IP Address</th>';
    echo '<th>Method</th>';
    echo '<th>Path</th>';
    echo '<th>Type</th>';
    echo '<th>Response</th>';
    echo '<th>Risk</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $risk_class = 'risk-level-' . strtolower($log['risk_level']);
        $response_class = $log['response_code'] >= 400 ? 'text-danger' : 'text-success';
        $blocked_badge = $log['blocked'] ? '<span class="badge bg-danger ms-1">BLOCKED</span>' : '';

        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'Anonymous') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td><span class="badge bg-secondary">' . htmlspecialchars($log['request_method']) . '</span></td>';
        echo '<td><code>' . htmlspecialchars(substr($log['request_uri'], 0, 50)) . (strlen($log['request_uri']) > 50 ? '...' : '') . '</code></td>';
        echo '<td><span class="badge bg-info">' . htmlspecialchars($log['access_type']) . '</span></td>';
        echo '<td><span class="' . $response_class . '">' . htmlspecialchars($log['response_code']) . '</span>' . $blocked_badge . '</td>';
        echo '<td><span class="log-level-badge ' . $risk_class . '">' . htmlspecialchars($log['risk_level']) . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'access\')">Details</button>';
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderFileOperationsLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No file operation logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>User</th>';
    echo '<th>Operation</th>';
    echo '<th>File Name</th>';
    echo '<th>Size</th>';
    echo '<th>Type</th>';
    echo '<th>Status</th>';
    echo '<th>Risk</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $risk_class = 'risk-level-' . strtolower($log['risk_level']);
        $status_class = $log['success'] ? 'text-success' : 'text-danger';
        $status_text = $log['success'] ? 'SUCCESS' : 'FAILED';

        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'Anonymous') . '</td>';
        echo '<td><span class="badge bg-primary">' . htmlspecialchars($log['operation_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars($log['file_name']) . '</td>';
        echo '<td>' . ($log['file_size'] ? number_format($log['file_size'] / 1024, 1) . ' KB' : 'N/A') . '</td>';
        echo '<td>' . htmlspecialchars($log['file_type'] ?? 'Unknown') . '</td>';
        echo '<td><span class="' . $status_class . '">' . $status_text . '</span></td>';
        echo '<td><span class="log-level-badge ' . $risk_class . '">' . htmlspecialchars($log['risk_level']) . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'file_operations\')">Details</button>';
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderSuspiciousLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No suspicious activity logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>User</th>';
    echo '<th>Activity Type</th>';
    echo '<th>Severity</th>';
    echo '<th>Description</th>';
    echo '<th>IP Address</th>';
    echo '<th>Status</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $severity_class = 'risk-level-' . strtolower($log['severity']);
        $blocked_badge = $log['auto_blocked'] ? '<span class="badge bg-danger">AUTO-BLOCKED</span>' : '';
        $resolved_badge = $log['resolved'] ? '<span class="badge bg-success">RESOLVED</span>' : '<span class="badge bg-warning">OPEN</span>';

        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'Anonymous') . '</td>';
        echo '<td><span class="badge bg-warning">' . htmlspecialchars($log['activity_type']) . '</span></td>';
        echo '<td><span class="log-level-badge ' . $severity_class . '">' . htmlspecialchars($log['severity']) . '</span></td>';
        echo '<td>' . htmlspecialchars(substr($log['description'], 0, 60)) . (strlen($log['description']) > 60 ? '...' : '') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . $blocked_badge . ' ' . $resolved_badge . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'suspicious\')">Details</button>';
        if (!$log['resolved']) {
            echo ' <button class="btn btn-sm btn-outline-success" onclick="resolveActivity(' . $log['id'] . ')">Resolve</button>';
        }
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderAdminActionsLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No admin action logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>Admin</th>';
    echo '<th>Action Type</th>';
    echo '<th>Description</th>';
    echo '<th>Target User</th>';
    echo '<th>IP Address</th>';
    echo '<th>Status</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $status_class = $log['success'] ? 'text-success' : 'text-danger';
        $status_text = $log['success'] ? 'SUCCESS' : 'FAILED';

        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['admin_username']) . '</td>';
        echo '<td><span class="badge bg-info">' . htmlspecialchars($log['action_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars(substr($log['action_description'], 0, 50)) . (strlen($log['action_description']) > 50 ? '...' : '') . '</td>';
        echo '<td>' . htmlspecialchars($log['target_username'] ?? 'N/A') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td><span class="' . $status_class . '">' . $status_text . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'admin_actions\')">Details</button>';
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderPerformanceLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No performance logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>Metric Type</th>';
    echo '<th>Metric Name</th>';
    echo '<th>Value</th>';
    echo '<th>Unit</th>';
    echo '<th>Threshold</th>';
    echo '<th>Time</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $threshold_class = $log['threshold_exceeded'] ? 'text-danger' : 'text-success';
        $threshold_text = $log['threshold_exceeded'] ? 'EXCEEDED' : 'NORMAL';

        echo '<tr>';
        echo '<td><span class="badge bg-secondary">' . htmlspecialchars($log['metric_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars($log['metric_name']) . '</td>';
        echo '<td>' . number_format($log['value'], 3) . '</td>';
        echo '<td>' . htmlspecialchars($log['unit']) . '</td>';
        echo '<td><span class="' . $threshold_class . '">' . $threshold_text . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}
?>
