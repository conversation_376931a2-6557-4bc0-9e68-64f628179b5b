<?php
session_start();
require '../config/db_connect.php';

// Ensure the user is an admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    exit('Access denied');
}

$type = $_GET['type'] ?? 'system';
$limit = (int)($_GET['limit'] ?? 100);
$date_range = $_GET['date_range'] ?? 'month';
$log_level = $_GET['log_level'] ?? '';
$user_filter = $_GET['user_filter'] ?? '';
$ip_filter = $_GET['ip_filter'] ?? '';
$start_date = $_GET['start_date'] ?? '';
$end_date = $_GET['end_date'] ?? '';

// Build date condition
$date_condition = '';
switch ($date_range) {
    case 'today':
        $date_condition = "AND DATE(created_at) = CURDATE()";
        break;
    case 'week':
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        break;
    case 'month':
        $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        break;
    case 'all':
        $date_condition = ""; // No date restriction
        break;
    case 'custom':
        if (!empty($start_date) && !empty($end_date)) {
            $date_condition = "AND DATE(created_at) BETWEEN " . $pdo->quote($start_date) . " AND " . $pdo->quote($end_date);
        } elseif (!empty($start_date)) {
            $date_condition = "AND DATE(created_at) >= " . $pdo->quote($start_date);
        } elseif (!empty($end_date)) {
            $date_condition = "AND DATE(created_at) <= " . $pdo->quote($end_date);
        }
        break;
    default:
        // For audit logs, use a more generous default
        if ($type === 'audit') {
            $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)"; // 3 months for audit logs
        } else {
            $date_condition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        }
        break;
}

// Build additional conditions
$additional_conditions = '';

// Log level filter (works for system, security, and other logs with level/risk fields)
if (!empty($log_level)) {
    switch ($type) {
        case 'system':
            $additional_conditions .= " AND log_level = " . $pdo->quote($log_level);
            break;
        case 'security':
        case 'access':
        case 'file_operations':
        case 'suspicious':
            $additional_conditions .= " AND risk_level = " . $pdo->quote($log_level);
            break;
        case 'anomaly':
            $additional_conditions .= " AND severity = " . $pdo->quote($log_level);
            break;
    }
}

// User filter
if (!empty($user_filter)) {
    switch ($type) {
        case 'admin_actions':
            $additional_conditions .= " AND (admin_username LIKE " . $pdo->quote('%' . $user_filter . '%') .
                                     " OR target_username LIKE " . $pdo->quote('%' . $user_filter . '%') . ")";
            break;
        case 'login':
            $additional_conditions .= " AND email LIKE " . $pdo->quote('%' . $user_filter . '%');
            break;
        default:
            $additional_conditions .= " AND username LIKE " . $pdo->quote('%' . $user_filter . '%');
            break;
    }
}

// IP address filter
if (!empty($ip_filter)) {
    $additional_conditions .= " AND ip_address LIKE " . $pdo->quote('%' . $ip_filter . '%');
}

try {
    switch ($type) {
        case 'system':
            $table_check = $pdo->query("SHOW TABLES LIKE 'system_logs'");
            if ($table_check->rowCount() > 0) {
                $query = "
                    SELECT id, log_level, category, message, details, ip_address, created_at
                    FROM system_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
                $logs = $pdo->query($query)->fetchAll();
                renderSystemLogs($logs);
            } else {
                echo '<div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>System Logs Not Available</h5>
                    <p>The system logs table has not been created yet. This feature tracks system-level events and messages.</p>
                    <p><a href="setup_logging.php" class="btn btn-primary btn-sm">Setup Basic Logging</a></p>
                </div>';
            }
            break;

        case 'security':
            $query = "
                SELECT id, event_type, username, ip_address, details, risk_level, created_at
                FROM security_logs 
                WHERE 1=1 $date_condition $additional_conditions
                ORDER BY created_at DESC 
                LIMIT $limit
            ";
            $logs = $pdo->query($query)->fetchAll();
            renderSecurityLogs($logs);
            break;

        case 'audit':
            // Check if audit_logs table exists, fallback to admin_logs if needed
            $table_check = $pdo->query("SHOW TABLES LIKE 'audit_logs'");
            if ($table_check->rowCount() > 0) {
                $query = "
                    SELECT id, action_type, table_name, record_id, username, old_values, new_values, ip_address, created_at
                    FROM audit_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
            } else {
                // Fallback to admin_logs table
                $query = "
                    SELECT id, action as action_type, target_type as table_name, target_id as record_id,
                           'admin' as username, NULL as old_values, details as new_values, ip_address, created_at
                    FROM admin_logs
                    WHERE 1=1 $date_condition
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
            }
            $logs = $pdo->query($query)->fetchAll();
            renderAuditLogs($logs);
            break;



        case 'error':
            $table_check = $pdo->query("SHOW TABLES LIKE 'error_logs'");
            if ($table_check->rowCount() > 0) {
                $query = "
                    SELECT id, error_type, error_message, file_path, line_number, user_id, ip_address, created_at
                    FROM error_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
                $logs = $pdo->query($query)->fetchAll();
                renderErrorLogs($logs);
            } else {
                echo '<div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Error Logs Not Available</h5>
                    <p>The error logs table has not been created yet. This feature tracks application errors and exceptions.</p>
                    <p><a href="setup_logging.php" class="btn btn-primary btn-sm">Setup Basic Logging</a></p>
                </div>';
            }
            break;

        case 'login':
            // Check if login_attempts table exists, fallback to security_logs
            $table_check = $pdo->query("SHOW TABLES LIKE 'login_attempts'");
            if ($table_check->rowCount() > 0) {
                $query = "
                    SELECT id, email, user_id, ip_address, country, city, user_agent, success, failure_reason, created_at
                    FROM login_attempts
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
            } else {
                // Fallback to security_logs for login events
                $query = "
                    SELECT id, username as email, user_id, ip_address, country, city, user_agent,
                           CASE WHEN event_type = 'LOGIN_SUCCESS' THEN 1 ELSE 0 END as success,
                           CASE WHEN event_type = 'LOGIN_FAILED' THEN 'Login failed' ELSE NULL END as failure_reason,
                           created_at
                    FROM security_logs
                    WHERE event_type IN ('LOGIN_SUCCESS', 'LOGIN_FAILED') $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
            }
            $logs = $pdo->query($query)->fetchAll();
            renderLoginLogs($logs);
            break;

        case 'anomaly':
            $table_check = $pdo->query("SHOW TABLES LIKE 'anomaly_logs'");
            if ($table_check->rowCount() > 0) {
                $query = "
                    SELECT id, anomaly_type, user_id, ip_address, severity, description, data, resolved, created_at
                    FROM anomaly_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
                $logs = $pdo->query($query)->fetchAll();
                renderAnomalyLogs($logs);
            } else {
                echo '<div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Anomaly Logs Not Available</h5>
                    <p>The anomaly logs table has not been created yet. This feature tracks unusual system behavior and patterns.</p>
                    <p><a href="setup_logging.php" class="btn btn-primary btn-sm">Setup Basic Logging</a></p>
                </div>';
            }
            break;

        case 'access':
            $table_check = $pdo->query("SHOW TABLES LIKE 'access_logs'");
            if ($table_check->rowCount() > 0) {
                $query = "
                    SELECT id, username, ip_address, request_method, request_uri, file_path, directory_path,
                           access_type, response_code, blocked, block_reason, risk_level, created_at
                    FROM access_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
                $logs = $pdo->query($query)->fetchAll();
                renderAccessLogs($logs);
            } else {
                echo '<div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Access Logs Not Available</h5>
                    <p>The access logs table has not been created yet. This feature tracks file and directory access.</p>
                    <p><a href="setup_enhanced_logging.php" class="btn btn-primary btn-sm">Setup Enhanced Logging</a></p>
                </div>';
            }
            break;

        case 'file_operations':
            $table_check = $pdo->query("SHOW TABLES LIKE 'file_operations_logs'");
            if ($table_check->rowCount() > 0) {
                $query = "
                    SELECT id, username, ip_address, operation_type, file_path, file_name,
                           file_size, file_type, success, error_message, risk_level, created_at
                    FROM file_operations_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
                $logs = $pdo->query($query)->fetchAll();
                renderFileOperationsLogs($logs);
            } else {
                echo '<div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>File Operations Logs Not Available</h5>
                    <p>The file operations logs table has not been created yet. This feature tracks file uploads, downloads, and modifications.</p>
                    <p><a href="setup_enhanced_logging.php" class="btn btn-primary btn-sm">Setup Enhanced Logging</a></p>
                </div>';
            }
            break;

        case 'suspicious':
            $table_check = $pdo->query("SHOW TABLES LIKE 'suspicious_activity_logs'");
            if ($table_check->rowCount() > 0) {
                $query = "
                    SELECT id, username, ip_address, activity_type, severity, description,
                           auto_blocked, resolved, resolved_by, created_at
                    FROM suspicious_activity_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
                $logs = $pdo->query($query)->fetchAll();
                renderSuspiciousLogs($logs);
            } else {
                echo '<div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Suspicious Activity Logs Not Available</h5>
                    <p>The suspicious activity logs table has not been created yet. This feature tracks potential security threats.</p>
                    <p><a href="setup_enhanced_logging.php" class="btn btn-primary btn-sm">Setup Enhanced Logging</a></p>
                </div>';
            }
            break;

        case 'admin_actions':
            // Check which admin logs table exists
            $admin_actions_exists = $pdo->query("SHOW TABLES LIKE 'admin_actions_logs'")->rowCount() > 0;
            $admin_logs_exists = $pdo->query("SHOW TABLES LIKE 'admin_logs'")->rowCount() > 0;

            if ($admin_actions_exists) {
                $query = "
                    SELECT id, admin_username, action_type, action_description, target_username,
                           ip_address, success, error_message, created_at
                    FROM admin_actions_logs
                    WHERE 1=1 $date_condition $additional_conditions
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
            } elseif ($admin_logs_exists) {
                $query = "
                    SELECT id, 'admin' as admin_username, action as action_type, details as action_description,
                           NULL as target_username, ip_address, 1 as success, NULL as error_message, created_at
                    FROM admin_logs
                    WHERE 1=1 $date_condition
                    ORDER BY created_at DESC
                    LIMIT $limit
                ";
            } else {
                echo '<div class="alert alert-warning">Admin action logs table not found. Please run the logging setup script.</div>';
                break;
            }
            $logs = $pdo->query($query)->fetchAll();
            renderAdminActionsLogs($logs);
            break;



        default:
            echo '<div class="alert alert-danger">Invalid log type</div>';
    }
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error loading logs: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

function renderSystemLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No system logs found</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Level</th>';
    echo '<th>Category</th>';
    echo '<th>Message</th>';
    echo '<th>IP Address</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        $level_class = 'log-level-' . strtolower($log['log_level']);
        echo '<tr>';
        echo '<td><span class="log-level-badge ' . $level_class . '">' . htmlspecialchars($log['log_level']) . '</span></td>';
        echo '<td>' . htmlspecialchars($log['category']) . '</td>';
        echo '<td>' . htmlspecialchars(substr($log['message'], 0, 100)) . (strlen($log['message']) > 100 ? '...' : '') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        if (!empty($log['details'])) {
            echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'system\')">Details</button>';
        }
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}

function renderSecurityLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No security logs found</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Event Type</th>';
    echo '<th>Username</th>';
    echo '<th>IP Address</th>';
    echo '<th>Risk Level</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        $risk_class = 'risk-level-' . strtolower($log['risk_level']);
        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['event_type']) . '</td>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'Unknown') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td><span class="log-level-badge ' . $risk_class . '">' . htmlspecialchars($log['risk_level']) . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        if (!empty($log['details'])) {
            echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'security\')">Details</button>';
        }
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}

function renderAuditLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4">';
        echo '<div class="alert alert-info">';
        echo '<h5><i class="fas fa-info-circle me-2"></i>No Audit Logs Found</h5>';
        echo '<p class="mb-2">No audit logs found for the selected date range.</p>';
        echo '<p class="mb-0"><strong>Try:</strong></p>';
        echo '<ul class="list-unstyled mb-0">';
        echo '<li>• Expanding the date range to "Last 30 days" or "All Time"</li>';
        echo '<li>• Making changes in <a href="../pages/settings.php" target="_blank">User Settings</a> to generate audit logs</li>';
        echo '<li>• Checking if audit logging is properly configured</li>';
        echo '</ul>';
        echo '</div>';
        echo '</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Action</th>';
    echo '<th>Table</th>';
    echo '<th>Record ID</th>';
    echo '<th>Username</th>';
    echo '<th>IP Address</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        echo '<tr>';
        echo '<td><span class="badge bg-primary">' . htmlspecialchars($log['action_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars($log['table_name']) . '</td>';
        echo '<td>' . htmlspecialchars($log['record_id'] ?? 'N/A') . '</td>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'System') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        if (!empty($log['old_values']) || !empty($log['new_values'])) {
            echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'audit\')">Changes</button>';
        }
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}



function renderErrorLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No error logs found</div>';
        return;
    }
    
    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Error Type</th>';
    echo '<th>Message</th>';
    echo '<th>File</th>';
    echo '<th>Line</th>';
    echo '<th>IP Address</th>';
    echo '<th>Time</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        echo '<tr>';
        echo '<td><span class="badge bg-danger">' . htmlspecialchars($log['error_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars(substr($log['error_message'], 0, 80)) . (strlen($log['error_message']) > 80 ? '...' : '') . '</td>';
        echo '<td>' . htmlspecialchars(basename($log['file_path'] ?? '')) . '</td>';
        echo '<td>' . htmlspecialchars($log['line_number'] ?? '') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
}

function renderLoginLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No login logs found</div>';
        return;
    }

    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Status</th>';
    echo '<th>Email</th>';
    echo '<th>User ID</th>';
    echo '<th>IP Address</th>';
    echo '<th>Location</th>';
    echo '<th>User Agent</th>';
    echo '<th>Reason</th>';
    echo '<th>Time</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $status = $log['success'] ? 'SUCCESS' : 'FAILED';
        $badge_class = $log['success'] ? 'success' : 'danger';
        echo '<tr>';
        echo '<td><span class="badge bg-' . $badge_class . '">' . $status . '</span></td>';
        echo '<td>' . htmlspecialchars($log['email']) . '</td>';
        echo '<td>' . ($log['user_id'] ? htmlspecialchars($log['user_id']) : '<span class="text-muted">N/A</span>') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . htmlspecialchars($log['country'] ?? 'Unknown') . ', ' . htmlspecialchars($log['city'] ?? 'Unknown') . '</td>';
        echo '<td><small>' . htmlspecialchars(substr($log['user_agent'] ?? '', 0, 30)) . '...</small></td>';
        echo '<td>' . htmlspecialchars($log['failure_reason'] ?? 'N/A') . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderAnomalyLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No anomaly logs found</div>';
        return;
    }

    echo '<table class="table table-hover mb-0">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Type</th>';
    echo '<th>Severity</th>';
    echo '<th>Description</th>';
    echo '<th>IP Address</th>';
    echo '<th>Status</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $severity_class = 'log-level-' . strtolower($log['severity']);
        $status = $log['resolved'] ? 'RESOLVED' : 'OPEN';
        $status_class = $log['resolved'] ? 'success' : 'warning';
        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['anomaly_type']) . '</td>';
        echo '<td><span class="log-level-badge ' . $severity_class . '">' . htmlspecialchars($log['severity']) . '</span></td>';
        echo '<td>' . htmlspecialchars(substr($log['description'], 0, 50)) . '...</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td><span class="badge bg-' . $status_class . '">' . $status . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        if (!empty($log['data'])) {
            echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'anomaly\')">Details</button>';
        }
        if (!$log['resolved']) {
            echo ' <button class="btn btn-sm btn-outline-success" onclick="resolveAnomaly(' . $log['id'] . ')">Resolve</button>';
        }
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderAccessLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No access logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>User</th>';
    echo '<th>IP Address</th>';
    echo '<th>Method</th>';
    echo '<th>Path</th>';
    echo '<th>Type</th>';
    echo '<th>Response</th>';
    echo '<th>Risk</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $risk_class = 'risk-level-' . strtolower($log['risk_level']);
        $response_class = $log['response_code'] >= 400 ? 'text-danger' : 'text-success';
        $blocked_badge = $log['blocked'] ? '<span class="badge bg-danger ms-1">BLOCKED</span>' : '';

        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'Anonymous') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td><span class="badge bg-secondary">' . htmlspecialchars($log['request_method']) . '</span></td>';
        echo '<td><code>' . htmlspecialchars(substr($log['request_uri'], 0, 50)) . (strlen($log['request_uri']) > 50 ? '...' : '') . '</code></td>';
        echo '<td><span class="badge bg-info">' . htmlspecialchars($log['access_type']) . '</span></td>';
        echo '<td><span class="' . $response_class . '">' . htmlspecialchars($log['response_code']) . '</span>' . $blocked_badge . '</td>';
        echo '<td><span class="log-level-badge ' . $risk_class . '">' . htmlspecialchars($log['risk_level']) . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'access\')">Details</button>';
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderFileOperationsLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No file operation logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>User</th>';
    echo '<th>Operation</th>';
    echo '<th>File Name</th>';
    echo '<th>Size</th>';
    echo '<th>Type</th>';
    echo '<th>Status</th>';
    echo '<th>Risk</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $risk_class = 'risk-level-' . strtolower($log['risk_level']);
        $status_class = $log['success'] ? 'text-success' : 'text-danger';
        $status_text = $log['success'] ? 'SUCCESS' : 'FAILED';

        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'Anonymous') . '</td>';
        echo '<td><span class="badge bg-primary">' . htmlspecialchars($log['operation_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars($log['file_name']) . '</td>';
        echo '<td>' . ($log['file_size'] ? number_format($log['file_size'] / 1024, 1) . ' KB' : 'N/A') . '</td>';
        echo '<td>' . htmlspecialchars($log['file_type'] ?? 'Unknown') . '</td>';
        echo '<td><span class="' . $status_class . '">' . $status_text . '</span></td>';
        echo '<td><span class="log-level-badge ' . $risk_class . '">' . htmlspecialchars($log['risk_level']) . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'file_operations\')">Details</button>';
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderSuspiciousLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No suspicious activity logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>User</th>';
    echo '<th>Activity Type</th>';
    echo '<th>Severity</th>';
    echo '<th>Description</th>';
    echo '<th>IP Address</th>';
    echo '<th>Status</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $severity_class = 'risk-level-' . strtolower($log['severity']);
        $blocked_badge = $log['auto_blocked'] ? '<span class="badge bg-danger">AUTO-BLOCKED</span>' : '';
        $resolved_badge = $log['resolved'] ? '<span class="badge bg-success">RESOLVED</span>' : '<span class="badge bg-warning">OPEN</span>';

        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['username'] ?? 'Anonymous') . '</td>';
        echo '<td><span class="badge bg-warning">' . htmlspecialchars($log['activity_type']) . '</span></td>';
        echo '<td><span class="log-level-badge ' . $severity_class . '">' . htmlspecialchars($log['severity']) . '</span></td>';
        echo '<td>' . htmlspecialchars(substr($log['description'], 0, 60)) . (strlen($log['description']) > 60 ? '...' : '') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td>' . $blocked_badge . ' ' . $resolved_badge . '</td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'suspicious\')">Details</button>';
        if (!$log['resolved']) {
            echo ' <button class="btn btn-sm btn-outline-success" onclick="resolveActivity(' . $log['id'] . ')">Resolve</button>';
        }
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

function renderAdminActionsLogs($logs) {
    if (empty($logs)) {
        echo '<div class="text-center p-4 text-muted">No admin action logs found</div>';
        return;
    }

    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>Admin</th>';
    echo '<th>Action Type</th>';
    echo '<th>Description</th>';
    echo '<th>Target User</th>';
    echo '<th>IP Address</th>';
    echo '<th>Status</th>';
    echo '<th>Time</th>';
    echo '<th>Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($logs as $log) {
        $status_class = $log['success'] ? 'text-success' : 'text-danger';
        $status_text = $log['success'] ? 'SUCCESS' : 'FAILED';

        echo '<tr>';
        echo '<td>' . htmlspecialchars($log['admin_username']) . '</td>';
        echo '<td><span class="badge bg-info">' . htmlspecialchars($log['action_type']) . '</span></td>';
        echo '<td>' . htmlspecialchars(substr($log['action_description'], 0, 50)) . (strlen($log['action_description']) > 50 ? '...' : '') . '</td>';
        echo '<td>' . htmlspecialchars($log['target_username'] ?? 'N/A') . '</td>';
        echo '<td>' . htmlspecialchars($log['ip_address']) . '</td>';
        echo '<td><span class="' . $status_class . '">' . $status_text . '</span></td>';
        echo '<td>' . date('M j, g:i A', strtotime($log['created_at'])) . '</td>';
        echo '<td>';
        echo '<button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(' . $log['id'] . ', \'admin_actions\')">Details</button>';
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}


?>
