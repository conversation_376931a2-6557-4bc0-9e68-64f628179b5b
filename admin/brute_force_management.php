<?php
session_start();
require '../config/db_connect.php';
require '../config/brute_force_protection.php';

// Set page variables for admin header
$page_title = 'Security Management';
$page_subtitle = 'Brute force protection and account security monitoring';

$bruteForceProtection = new BruteForceProtection($pdo);

// Handle unlock account action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['unlock_email'])) {
    $email = $_POST['unlock_email'];
    $bruteForceProtection->clearFailedAttempts($email);
    $success_message = "Account unlocked successfully for: " . htmlspecialchars($email);
}

// Get lockout statistics
$stats = $bruteForceProtection->getLockoutStats();

// Initialize variables to prevent conflicts
$locked_accounts_list = [];
$recent_attempts_list = [];

// Get current locked accounts
try {
    // Check if table exists first
    $table_check = $pdo->query("SHOW TABLES LIKE 'account_lockouts'");
    if (!$table_check || !$table_check->fetch()) {
        error_log("Table 'account_lockouts' does not exist");
        $locked_accounts_list = [];
    } else {
        $stmt = $pdo->query("
            SELECT email, locked_at, unlock_at, attempt_count, ip_address,
                   TIMESTAMPDIFF(SECOND, NOW(), unlock_at) as remaining_seconds,
                   NOW() as now_time,
                   (unlock_at > NOW()) as is_locked
            FROM account_lockouts
            WHERE unlock_at > NOW()
            ORDER BY locked_at DESC
        ");

        if ($stmt) {
            $locked_accounts_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
            if (!is_array($locked_accounts_list)) {
                error_log("fetchAll() returned non-array: " . gettype($locked_accounts_list));
                $locked_accounts_list = [];
            }
        } else {
            error_log("Query failed for locked accounts");
            $locked_accounts_list = [];
        }
    }
} catch (Exception $e) {
    error_log("Error fetching locked accounts: " . $e->getMessage());
    $locked_accounts_list = [];
}

// Ensure it's always an array and assign to final variable
if (!is_array($locked_accounts_list)) {
    error_log("Forcing locked_accounts_list to be array, was: " . gettype($locked_accounts_list));
    $locked_accounts_list = [];
}
$locked_accounts = $locked_accounts_list;

// Get recent failed attempts
try {
    // Check if table exists first
    $table_check = $pdo->query("SHOW TABLES LIKE 'brute_force_attempts'");
    if (!$table_check || !$table_check->fetch()) {
        error_log("Table 'brute_force_attempts' does not exist");
        $recent_attempts_list = [];
    } else {
        $stmt = $pdo->query("
            SELECT email, ip_address, attempt_time, user_agent,
                   COUNT(*) as attempt_count
            FROM brute_force_attempts
            WHERE attempt_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            GROUP BY email, ip_address
            ORDER BY attempt_time DESC
            LIMIT 20
        ");

        if ($stmt) {
            $recent_attempts_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
            if (!is_array($recent_attempts_list)) {
                error_log("fetchAll() returned non-array: " . gettype($recent_attempts_list));
                $recent_attempts_list = [];
            }
        } else {
            error_log("Query failed for recent attempts");
            $recent_attempts_list = [];
        }
    }
} catch (Exception $e) {
    error_log("Error fetching recent attempts: " . $e->getMessage());
    $recent_attempts_list = [];
}

// Ensure it's always an array and assign to final variable
if (!is_array($recent_attempts_list)) {
    error_log("Forcing recent_attempts_list to be array, was: " . gettype($recent_attempts_list));
    $recent_attempts_list = [];
}
$recent_attempts = $recent_attempts_list;

include 'includes/admin_header.php';
?>

<style>
    .security-stat-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 24px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .security-stat-card:hover {
        border-color: #d1d5db;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-1px);
    }

    .security-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #6366f1, #8b5cf6);
    }

    .security-stat-card.danger::before {
        background: linear-gradient(90deg, #ef4444, #f97316);
    }

    .security-stat-card.warning::before {
        background: linear-gradient(90deg, #f59e0b, #eab308);
    }

    .security-stat-card.success::before {
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .security-stat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .security-stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
    }

    .security-stat-icon.danger {
        background: linear-gradient(135deg, #ef4444, #f97316);
    }

    .security-stat-icon.warning {
        background: linear-gradient(135deg, #f59e0b, #eab308);
    }

    .security-stat-icon.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .security-stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #111827;
        margin: 8px 0 4px 0;
        line-height: 1;
    }

    .security-stat-label {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
        margin: 0 0 12px 0;
    }

    .security-stat-status {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
    }

    .security-stat-status.secure {
        background: #ecfdf5;
        color: #065f46;
    }

    .security-stat-status.alert {
        background: #fef2f2;
        color: #991b1b;
    }

    .security-stat-status.warning {
        background: #fffbeb;
        color: #92400e;
    }

    .security-table {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        overflow: hidden;
    }

    .security-table-header {
        background: #f9fafb;
        padding: 20px 24px;
        border-bottom: 1px solid #e5e7eb;
    }

    .security-table-title {
        font-size: 16px;
        font-weight: 600;
        color: #111827;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-unlock {
        background: #10b981;
        border: 1px solid #10b981;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }

    .btn-unlock:hover {
        background: #059669;
        border-color: #059669;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
    }

    .table th {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        color: #495057;
        font-weight: 500;
        font-size: 14px;
        padding: 12px 16px;
        border-top: none;
    }

    .table td {
        padding: 12px 16px;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
        color: #495057;
        font-size: 14px;
    }

    .table tbody tr:hover {
        background: #f8f9fa;
    }

    .table {
        margin-bottom: 0;
        border: none;
        width: 100%;
        table-layout: fixed;
    }

    .table th:nth-child(1), .table td:nth-child(1) { width: 25%; } /* Email */
    .table th:nth-child(2), .table td:nth-child(2) { width: 12%; } /* IP Address */
    .table th:nth-child(3), .table td:nth-child(3) { width: 15%; } /* Locked At */
    .table th:nth-child(4), .table td:nth-child(4) { width: 15%; } /* Unlock At */
    .table th:nth-child(5), .table td:nth-child(5) { width: 8%; }  /* Attempts */
    .table th:nth-child(6), .table td:nth-child(6) { width: 15%; } /* Remaining Time */
    .table th:nth-child(7), .table td:nth-child(7) { width: 10%; } /* Action */

    /* For Recent Attempts table */
    .recent-attempts-table th:nth-child(1), .recent-attempts-table td:nth-child(1) { width: 25%; } /* Email */
    .recent-attempts-table th:nth-child(2), .recent-attempts-table td:nth-child(2) { width: 12%; } /* IP Address */
    .recent-attempts-table th:nth-child(3), .recent-attempts-table td:nth-child(3) { width: 15%; } /* Last Attempt */
    .recent-attempts-table th:nth-child(4), .recent-attempts-table td:nth-child(4) { width: 10%; } /* Attempt Count */
    .recent-attempts-table th:nth-child(5), .recent-attempts-table td:nth-child(5) { width: 38%; } /* User Agent */

    .table td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--gray-500);
    }

    .empty-state i {
        font-size: 48px;
        margin-bottom: 16px;
        color: var(--gray-400);
    }

    .refresh-indicator {
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 8px 16px;
        border-radius: var(--border-radius);
        font-size: 12px;
        z-index: 1000;
        display: none;
    }
</style>
<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Refresh Indicator -->
<div class="refresh-indicator" id="refreshIndicator">
    <i class="fas fa-sync-alt fa-spin me-2"></i>Auto-refreshing...
</div>

<!-- Security Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-lg-4">
        <div class="security-stat-card <?= ($stats['current_locked'] ?? 0) > 0 ? 'danger' : 'success' ?>">
            <div class="security-stat-header">
                <div class="security-stat-icon <?= ($stats['current_locked'] ?? 0) > 0 ? 'danger' : 'success' ?>">
                    <i class="fas fa-<?= ($stats['current_locked'] ?? 0) > 0 ? 'lock' : 'shield-check' ?>"></i>
                </div>
            </div>
            <div class="security-stat-number"><?= number_format($stats['current_locked'] ?? 0) ?></div>
            <p class="security-stat-label">Currently Locked Accounts</p>
            <div class="security-stat-status <?= ($stats['current_locked'] ?? 0) > 0 ? 'alert' : 'secure' ?>">
                <i class="fas fa-<?= ($stats['current_locked'] ?? 0) > 0 ? 'exclamation-triangle' : 'check-circle' ?>"></i>
                <?= ($stats['current_locked'] ?? 0) > 0 ? 'Security Alert' : 'All Clear' ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="security-stat-card <?= ($stats['recent_attempts'] ?? 0) > 10 ? 'danger' : (($stats['recent_attempts'] ?? 0) > 5 ? 'warning' : 'success') ?>">
            <div class="security-stat-header">
                <div class="security-stat-icon <?= ($stats['recent_attempts'] ?? 0) > 10 ? 'danger' : (($stats['recent_attempts'] ?? 0) > 5 ? 'warning' : 'success') ?>">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
            <div class="security-stat-number"><?= number_format($stats['recent_attempts'] ?? 0) ?></div>
            <p class="security-stat-label">Failed Attempts (Last Hour)</p>
            <div class="security-stat-status <?= ($stats['recent_attempts'] ?? 0) > 10 ? 'alert' : (($stats['recent_attempts'] ?? 0) > 5 ? 'warning' : 'secure') ?>">
                <i class="fas fa-<?= ($stats['recent_attempts'] ?? 0) > 10 ? 'arrow-up' : 'arrow-down' ?>"></i>
                <?= ($stats['recent_attempts'] ?? 0) > 10 ? 'High Activity' : (($stats['recent_attempts'] ?? 0) > 5 ? 'Moderate' : 'Normal') ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="security-stat-card warning">
            <div class="security-stat-header">
                <div class="security-stat-icon warning">
                    <i class="fas fa-bullseye"></i>
                </div>
            </div>
            <div class="security-stat-number"><?= number_format(is_array($stats['top_targeted'] ?? []) ? count($stats['top_targeted']) : 0) ?></div>
            <p class="security-stat-label">Targeted Accounts (24h)</p>
            <div class="security-stat-status warning">
                <i class="fas fa-crosshairs"></i>
                Monitoring
            </div>
        </div>
    </div>
</div>

<!-- Currently Locked Accounts -->
<div class="security-table mb-4">
    <div class="security-table-header">
        <h3 class="security-table-title">
            <i class="fas fa-lock text-danger"></i>
            Currently Locked Accounts
            <?php if (!empty($locked_accounts) && is_array($locked_accounts)): ?>
                <span class="badge bg-danger ms-2"><?= count($locked_accounts) ?></span>
            <?php endif; ?>
        </h3>
    </div>
    <div class="table-responsive">
        <?php if (empty($locked_accounts) || !is_array($locked_accounts)): ?>
            <div class="empty-state">
                <i class="fas fa-shield-check text-success"></i>
                <h5>No accounts are currently locked</h5>
                <p>All accounts are accessible for login</p>
            </div>
        <?php else: ?>
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Email</th>
                        <th>IP Address</th>
                        <th>Locked At</th>
                        <th>Unlock At</th>
                        <th>Attempts</th>
                        <th>Remaining Time</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ((array)$locked_accounts as $account): ?>
                        <?php if (is_array($account) && isset($account['email'])): ?>
                        <tr>
                            <td><?= htmlspecialchars($account['email']) ?></td>
                            <td><?= htmlspecialchars($account['ip_address'] ?? 'N/A') ?></td>
                            <td><?= isset($account['locked_at']) ? date('M j, Y H:i', strtotime($account['locked_at'])) : 'N/A' ?></td>
                            <td><?= isset($account['unlock_at']) ? date('M j, Y H:i', strtotime($account['unlock_at'])) : 'N/A' ?></td>
                            <td><span class="badge bg-danger"><?= $account['attempt_count'] ?? 0 ?></span></td>
                            <td class="text-warning">
                                <?php
                                $remaining = max(0, $account['remaining_seconds'] ?? 0);
                                echo $bruteForceProtection->formatRemainingTime($remaining);
                                ?>
                            </td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="unlock_email" value="<?= htmlspecialchars($account['email']) ?>">
                                    <button type="submit" class="btn btn-unlock btn-sm"
                                            onclick="return confirm('Are you sure you want to unlock this account?')">
                                        <i class="fas fa-unlock me-1"></i>Unlock
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<!-- Recent Failed Attempts -->
<div class="security-table">
    <div class="security-table-header">
        <h3 class="security-table-title">
            <i class="fas fa-exclamation-triangle text-warning"></i>
            Recent Failed Attempts (Last Hour)
            <?php if (!empty($recent_attempts) && is_array($recent_attempts)): ?>
                <span class="badge bg-warning text-dark ms-2"><?= count($recent_attempts) ?></span>
            <?php endif; ?>
        </h3>
    </div>
    <div class="table-responsive">
        <?php if (empty($recent_attempts) || !is_array($recent_attempts)): ?>
            <div class="empty-state">
                <i class="fas fa-shield-check text-success"></i>
                <h5>No recent failed attempts</h5>
                <p>System is secure with no suspicious activity</p>
            </div>
        <?php else: ?>
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Email</th>
                        <th>IP Address</th>
                        <th>Last Attempt</th>
                        <th>Attempt Count</th>
                        <th>User Agent</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ((array)$recent_attempts as $attempt): ?>
                        <?php if (is_array($attempt) && isset($attempt['email'])): ?>
                        <tr>
                            <td><?= htmlspecialchars($attempt['email']) ?></td>
                            <td><?= htmlspecialchars($attempt['ip_address'] ?? 'N/A') ?></td>
                            <td><?= isset($attempt['attempt_time']) ? date('M j, Y H:i', strtotime($attempt['attempt_time'])) : 'N/A' ?></td>
                            <td>
                                <span class="badge bg-<?= ($attempt['attempt_count'] ?? 0) >= 5 ? 'danger' : (($attempt['attempt_count'] ?? 0) >= 3 ? 'warning' : 'secondary') ?>">
                                    <?= $attempt['attempt_count'] ?? 0 ?>
                                </span>
                            </td>
                            <td class="text-truncate" style="max-width: 300px;" title="<?= htmlspecialchars($attempt['user_agent'] ?? '') ?>">
                                <?= htmlspecialchars(substr($attempt['user_agent'] ?? '', 0, 60)) . (strlen($attempt['user_agent'] ?? '') > 60 ? '...' : '') ?>
                            </td>
                        </tr>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mt-4">
    <div class="col-lg-6">
        <div class="security-table">
            <div class="security-table-header">
                <h3 class="security-table-title">
                    <i class="fas fa-tools text-primary"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="p-4">
                <div class="d-grid gap-2">
                    <a href="../auth/test_brute_force.php" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-vial me-2"></i>Test Brute Force Protection
                    </a>
                    <a href="../auth/test_lockout_enforcement.php" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-lock me-2"></i>Test Lockout Enforcement
                    </a>
                    <a href="logs.php" class="btn btn-outline-secondary">
                        <i class="fas fa-clipboard-list me-2"></i>View System Logs
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="security-table">
            <div class="security-table-header">
                <h3 class="security-table-title">
                    <i class="fas fa-info-circle text-info"></i>
                    Protection Settings
                </h3>
            </div>
            <div class="p-4">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-primary mb-1">5</div>
                            <small class="text-muted">Max Attempts</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-warning mb-1">5 min</div>
                            <small class="text-muted">Lockout Duration</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Protection is <strong class="text-success">ACTIVE</strong>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced auto-refresh with indicator
let refreshTimer;
let refreshCountdown = 30;

function startRefreshCountdown() {
    const indicator = document.getElementById('refreshIndicator');

    refreshTimer = setInterval(() => {
        refreshCountdown--;

        if (refreshCountdown <= 5) {
            indicator.style.display = 'block';
        }

        if (refreshCountdown <= 0) {
            location.reload();
        }
    }, 1000);
}

// Start countdown on page load
startRefreshCountdown();

// Reset countdown on user interaction
document.addEventListener('click', () => {
    refreshCountdown = 30;
    document.getElementById('refreshIndicator').style.display = 'none';
});

// Simple table interactions
document.querySelectorAll('table tbody tr').forEach(row => {
    row.addEventListener('mouseenter', function() {
        this.style.backgroundColor = '#f8f9fa';
    });

    row.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
    });
});
</script>

<?php include 'includes/admin_footer.php'; ?>
